import{d as w,u as h,h as f,i as k,c as d,a as t,e as m,f as C,w as L,r as j,j as V,k as y,l as u,v as x,m as M,t as b,b as S,g as _,o as c}from"./index-CC71qafK.js";const D={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},A={class:"max-w-md w-full space-y-8"},B={class:"mt-2 text-center text-sm text-gray-600"},N={class:"space-y-4"},R={class:"flex items-center justify-between"},z={class:"flex items-center"},E={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4"},U={class:"flex"},$={class:"ml-3"},q={class:"text-sm text-red-800"},P=["disabled"],T={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},F={class:"mt-6"},G={class:"mt-6 grid grid-cols-1 gap-3"},I=w({__name:"LoginView",setup(H){const g=_(),v=h(),a=f(!1),l=f(""),s=k({email:"",password:"",rememberMe:!1}),p=async()=>{if(!s.email||!s.password){l.value="Please fill in all fields";return}a.value=!0,l.value="";try{const r=await v.login(s.email,s.password);if(r.success){const n={buyer:"buyer-dashboard",seller:"seller-dashboard",admin:"admin-dashboard"}[r.user.role];g.push({name:n})}else l.value=r.error||"Login failed"}catch(r){l.value="An unexpected error occurred",console.error("Login error:",r)}finally{a.value=!1}},i=async r=>{const e={buyer:{email:"<EMAIL>",password:"demo123"},seller:{email:"<EMAIL>",password:"demo123"},admin:{email:"<EMAIL>",password:"demo123"}};s.email=e[r].email,s.password=e[r].password,await p()};return(r,e)=>{const n=j("router-link");return c(),d("div",D,[t("div",A,[t("div",null,[e[8]||(e[8]=t("div",{class:"flex justify-center"},[t("div",{class:"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center"},[t("span",{class:"text-white font-bold text-xl"},"G")])],-1)),e[9]||(e[9]=t("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Sign in to your account ",-1)),t("p",B,[e[7]||(e[7]=m(" Or ")),C(n,{to:"/signup",class:"font-medium text-primary-600 hover:text-primary-500"},{default:L(()=>e[6]||(e[6]=[m(" create a new account ")])),_:1,__:[6]})])]),t("form",{class:"mt-8 space-y-6",onSubmit:V(p,["prevent"])},[t("div",N,[t("div",null,[e[10]||(e[10]=t("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Email address ",-1)),u(t("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=o=>s.email=o),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"Enter your email"},null,512),[[x,s.email]])]),t("div",null,[e[11]||(e[11]=t("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Password ",-1)),u(t("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=o=>s.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Enter your password"},null,512),[[x,s.password]])])]),t("div",R,[t("div",z,[u(t("input",{id:"remember-me","onUpdate:modelValue":e[2]||(e[2]=o=>s.rememberMe=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[M,s.rememberMe]]),e[12]||(e[12]=t("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Remember me ",-1))]),e[13]||(e[13]=t("div",{class:"text-sm"},[t("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Forgot your password? ")],-1))]),l.value?(c(),d("div",E,[t("div",U,[e[14]||(e[14]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),t("div",$,[t("p",q,b(l.value),1)])])])):y("",!0),t("div",null,[t("button",{type:"submit",disabled:a.value,class:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[a.value?(c(),d("span",T,e[15]||(e[15]=[t("svg",{class:"animate-spin h-5 w-5 text-primary-300",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):y("",!0),m(" "+b(a.value?"Signing in...":"Sign in"),1)],8,P)]),t("div",F,[e[16]||(e[16]=S('<div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-50 text-gray-500">Demo Accounts</span></div></div>',1)),t("div",G,[t("button",{type:"button",onClick:e[3]||(e[3]=o=>i("buyer")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200"}," Login as Demo Buyer "),t("button",{type:"button",onClick:e[4]||(e[4]=o=>i("seller")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200"}," Login as Demo Seller "),t("button",{type:"button",onClick:e[5]||(e[5]=o=>i("admin")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200"}," Login as Demo Admin ")])])],32)])])}}});export{I as default};
