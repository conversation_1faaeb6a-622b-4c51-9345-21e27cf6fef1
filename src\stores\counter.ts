import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'

export interface User {
  id: string
  email: string
  role: 'buyer' | 'seller' | 'admin'
  name?: string
  businessName?: string
  businessLocation?: string
  businessPhone?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || null)

  // Set axios default authorization header if token exists
  if (token.value) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
  }

  async function login(email: string, password: string) {
    try {
      // Mock API call - replace with actual API endpoint
      const response = await axios.post('/api/auth/login', { email, password })

      const { user: userData, token: authToken } = response.data

      user.value = userData
      token.value = authToken

      localStorage.setItem('token', authToken)
      localStorage.setItem('user', JSON.stringify(userData))
      axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`

      return { success: true, user: userData }
    } catch (error: any) {
      console.error('Login error:', error)
      return {
        success: false,
        error: error.response?.data?.message || 'Login failed'
      }
    }
  }

  async function register(userData: {
    email: string
    password: string
    role: 'buyer' | 'seller'
    name?: string
    businessName?: string
    businessLocation?: string
    businessPhone?: string
  }) {
    try {
      // Mock API call - replace with actual API endpoint
      const response = await axios.post('/api/auth/register', userData)

      const { user: newUser, token: authToken } = response.data

      user.value = newUser
      token.value = authToken

      localStorage.setItem('token', authToken)
      localStorage.setItem('user', JSON.stringify(newUser))
      axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`

      return { success: true, user: newUser }
    } catch (error: any) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: error.response?.data?.message || 'Registration failed'
      }
    }
  }

  function logout() {
    user.value = null
    token.value = null

    localStorage.removeItem('token')
    localStorage.removeItem('user')
    delete axios.defaults.headers.common['Authorization']
  }

  function initializeAuth() {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')

    if (storedToken && storedUser) {
      try {
        token.value = storedToken
        user.value = JSON.parse(storedUser)
        axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
      } catch (error) {
        console.error('Error parsing stored user data:', error)
        logout()
      }
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    userRole,
    login,
    register,
    logout,
    initializeAuth
  }
})
