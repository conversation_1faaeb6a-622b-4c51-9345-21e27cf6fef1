(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();function ti(e,t){return function(){return e.apply(t,arguments)}}const{toString:Xl}=Object.prototype,{getPrototypeOf:ur}=Object,{iterator:is,toStringTag:ni}=Symbol,ls=(e=>t=>{const n=Xl.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Je=e=>(e=e.toLowerCase(),t=>ls(t)===e),cs=e=>t=>typeof t===e,{isArray:tn}=Array,wn=cs("undefined");function Ql(e){return e!==null&&!wn(e)&&e.constructor!==null&&!wn(e.constructor)&&Le(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const si=Je("ArrayBuffer");function Yl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&si(e.buffer),t}const Zl=cs("string"),Le=cs("function"),ri=cs("number"),as=e=>e!==null&&typeof e=="object",ec=e=>e===!0||e===!1,Bn=e=>{if(ls(e)!=="object")return!1;const t=ur(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ni in e)&&!(is in e)},tc=Je("Date"),nc=Je("File"),sc=Je("Blob"),rc=Je("FileList"),oc=e=>as(e)&&Le(e.pipe),ic=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Le(e.append)&&((t=ls(e))==="formdata"||t==="object"&&Le(e.toString)&&e.toString()==="[object FormData]"))},lc=Je("URLSearchParams"),[cc,ac,uc,fc]=["ReadableStream","Request","Response","Headers"].map(Je),dc=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Cn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),tn(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function oi(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Mt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ii=e=>!wn(e)&&e!==Mt;function Vs(){const{caseless:e}=ii(this)&&this||{},t={},n=(s,r)=>{const o=e&&oi(t,r)||r;Bn(t[o])&&Bn(s)?t[o]=Vs(t[o],s):Bn(s)?t[o]=Vs({},s):tn(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Cn(arguments[s],n);return t}const hc=(e,t,n,{allOwnKeys:s}={})=>(Cn(t,(r,o)=>{n&&Le(r)?e[o]=ti(r,n):e[o]=r},{allOwnKeys:s}),e),pc=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),mc=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},gc=(e,t,n,s)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&ur(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},yc=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},bc=e=>{if(!e)return null;if(tn(e))return e;let t=e.length;if(!ri(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},_c=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ur(Uint8Array)),vc=(e,t)=>{const s=(e&&e[is]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},wc=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},xc=Je("HTMLFormElement"),Ec=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),$r=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Sc=Je("RegExp"),li=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Cn(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},Rc=e=>{li(e,(t,n)=>{if(Le(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Le(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Ac=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return tn(e)?s(e):s(String(e).split(t)),n},Oc=()=>{},Tc=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Cc(e){return!!(e&&Le(e.append)&&e[ni]==="FormData"&&e[is])}const Pc=e=>{const t=new Array(10),n=(s,r)=>{if(as(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=tn(s)?[]:{};return Cn(s,(i,l)=>{const a=n(i,r+1);!wn(a)&&(o[l]=a)}),t[r]=void 0,o}}return s};return n(e,0)},Ic=Je("AsyncFunction"),kc=e=>e&&(as(e)||Le(e))&&Le(e.then)&&Le(e.catch),ci=((e,t)=>e?setImmediate:t?((n,s)=>(Mt.addEventListener("message",({source:r,data:o})=>{r===Mt&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),Mt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Le(Mt.postMessage)),Nc=typeof queueMicrotask<"u"?queueMicrotask.bind(Mt):typeof process<"u"&&process.nextTick||ci,Lc=e=>e!=null&&Le(e[is]),y={isArray:tn,isArrayBuffer:si,isBuffer:Ql,isFormData:ic,isArrayBufferView:Yl,isString:Zl,isNumber:ri,isBoolean:ec,isObject:as,isPlainObject:Bn,isReadableStream:cc,isRequest:ac,isResponse:uc,isHeaders:fc,isUndefined:wn,isDate:tc,isFile:nc,isBlob:sc,isRegExp:Sc,isFunction:Le,isStream:oc,isURLSearchParams:lc,isTypedArray:_c,isFileList:rc,forEach:Cn,merge:Vs,extend:hc,trim:dc,stripBOM:pc,inherits:mc,toFlatObject:gc,kindOf:ls,kindOfTest:Je,endsWith:yc,toArray:bc,forEachEntry:vc,matchAll:wc,isHTMLForm:xc,hasOwnProperty:$r,hasOwnProp:$r,reduceDescriptors:li,freezeMethods:Rc,toObjectSet:Ac,toCamelCase:Ec,noop:Oc,toFiniteNumber:Tc,findKey:oi,global:Mt,isContextDefined:ii,isSpecCompliantForm:Cc,toJSONObject:Pc,isAsyncFn:Ic,isThenable:kc,setImmediate:ci,asap:Nc,isIterable:Lc};function q(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}y.inherits(q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const ai=q.prototype,ui={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ui[e]={value:e}});Object.defineProperties(q,ui);Object.defineProperty(ai,"isAxiosError",{value:!0});q.from=(e,t,n,s,r,o)=>{const i=Object.create(ai);return y.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),q.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Mc=null;function Ks(e){return y.isPlainObject(e)||y.isArray(e)}function fi(e){return y.endsWith(e,"[]")?e.slice(0,-2):e}function Hr(e,t,n){return e?e.concat(t).map(function(r,o){return r=fi(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function Fc(e){return y.isArray(e)&&!e.some(Ks)}const jc=y.toFlatObject(y,{},null,function(t){return/^is[A-Z]/.test(t)});function us(e,t,n){if(!y.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,S){return!y.isUndefined(S[x])});const s=n.metaTokens,r=n.visitor||c,o=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(t);if(!y.isFunction(r))throw new TypeError("visitor must be a function");function u(b){if(b===null)return"";if(y.isDate(b))return b.toISOString();if(!a&&y.isBlob(b))throw new q("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(b)||y.isTypedArray(b)?a&&typeof Blob=="function"?new Blob([b]):Buffer.from(b):b}function c(b,x,S){let C=b;if(b&&!S&&typeof b=="object"){if(y.endsWith(x,"{}"))x=s?x:x.slice(0,-2),b=JSON.stringify(b);else if(y.isArray(b)&&Fc(b)||(y.isFileList(b)||y.endsWith(x,"[]"))&&(C=y.toArray(b)))return x=fi(x),C.forEach(function(I,L){!(y.isUndefined(I)||I===null)&&t.append(i===!0?Hr([x],L,o):i===null?x:x+"[]",u(I))}),!1}return Ks(b)?!0:(t.append(Hr(S,x,o),u(b)),!1)}const f=[],p=Object.assign(jc,{defaultVisitor:c,convertValue:u,isVisitable:Ks});function m(b,x){if(!y.isUndefined(b)){if(f.indexOf(b)!==-1)throw Error("Circular reference detected in "+x.join("."));f.push(b),y.forEach(b,function(C,O){(!(y.isUndefined(C)||C===null)&&r.call(t,C,y.isString(O)?O.trim():O,x,p))===!0&&m(C,x?x.concat(O):[O])}),f.pop()}}if(!y.isObject(e))throw new TypeError("data must be an object");return m(e),t}function qr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function fr(e,t){this._pairs=[],e&&us(e,this,t)}const di=fr.prototype;di.append=function(t,n){this._pairs.push([t,n])};di.toString=function(t){const n=t?function(s){return t.call(this,s,qr)}:qr;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function Dc(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function hi(e,t,n){if(!t)return e;const s=n&&n.encode||Dc;y.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=y.isURLSearchParams(t)?t.toString():new fr(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Vr{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){y.forEach(this.handlers,function(s){s!==null&&t(s)})}}const pi={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Uc=typeof URLSearchParams<"u"?URLSearchParams:fr,Bc=typeof FormData<"u"?FormData:null,$c=typeof Blob<"u"?Blob:null,Hc={isBrowser:!0,classes:{URLSearchParams:Uc,FormData:Bc,Blob:$c},protocols:["http","https","file","blob","url","data"]},dr=typeof window<"u"&&typeof document<"u",Ws=typeof navigator=="object"&&navigator||void 0,qc=dr&&(!Ws||["ReactNative","NativeScript","NS"].indexOf(Ws.product)<0),Vc=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Kc=dr&&window.location.href||"http://localhost",Wc=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:dr,hasStandardBrowserEnv:qc,hasStandardBrowserWebWorkerEnv:Vc,navigator:Ws,origin:Kc},Symbol.toStringTag,{value:"Module"})),we={...Wc,...Hc};function zc(e,t){return us(e,new we.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return we.isNode&&y.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Jc(e){return y.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Gc(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function mi(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=n.length;return i=!i&&y.isArray(r)?r.length:i,a?(y.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!l):((!r[i]||!y.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&y.isArray(r[i])&&(r[i]=Gc(r[i])),!l)}if(y.isFormData(e)&&y.isFunction(e.entries)){const n={};return y.forEachEntry(e,(s,r)=>{t(Jc(s),r,n,0)}),n}return null}function Xc(e,t,n){if(y.isString(e))try{return(t||JSON.parse)(e),y.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Pn={transitional:pi,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=y.isObject(t);if(o&&y.isHTMLForm(t)&&(t=new FormData(t)),y.isFormData(t))return r?JSON.stringify(mi(t)):t;if(y.isArrayBuffer(t)||y.isBuffer(t)||y.isStream(t)||y.isFile(t)||y.isBlob(t)||y.isReadableStream(t))return t;if(y.isArrayBufferView(t))return t.buffer;if(y.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return zc(t,this.formSerializer).toString();if((l=y.isFileList(t))||s.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return us(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),Xc(t)):t}],transformResponse:[function(t){const n=this.transitional||Pn.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(y.isResponse(t)||y.isReadableStream(t))return t;if(t&&y.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?q.from(l,q.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],e=>{Pn.headers[e]={}});const Qc=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Yc=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&Qc[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Kr=Symbol("internals");function on(e){return e&&String(e).trim().toLowerCase()}function $n(e){return e===!1||e==null?e:y.isArray(e)?e.map($n):String(e)}function Zc(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const ea=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ts(e,t,n,s,r){if(y.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!y.isString(t)){if(y.isString(s))return t.indexOf(s)!==-1;if(y.isRegExp(s))return s.test(t)}}function ta(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function na(e,t){const n=y.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}let Me=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(l,a,u){const c=on(a);if(!c)throw new Error("header name must be a non-empty string");const f=y.findKey(r,c);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||a]=$n(l))}const i=(l,a)=>y.forEach(l,(u,c)=>o(u,c,a));if(y.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(y.isString(t)&&(t=t.trim())&&!ea(t))i(Yc(t),n);else if(y.isObject(t)&&y.isIterable(t)){let l={},a,u;for(const c of t){if(!y.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?y.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=on(t),t){const s=y.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Zc(r);if(y.isFunction(n))return n.call(this,r,s);if(y.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=on(t),t){const s=y.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ts(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=on(i),i){const l=y.findKey(s,i);l&&(!n||Ts(s,s[l],l,n))&&(delete s[l],r=!0)}}return y.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||Ts(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return y.forEach(this,(r,o)=>{const i=y.findKey(s,o);if(i){n[i]=$n(r),delete n[o];return}const l=t?ta(o):String(o).trim();l!==o&&delete n[o],n[l]=$n(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return y.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&y.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Kr]=this[Kr]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=on(i);s[l]||(na(r,i),s[l]=!0)}return y.isArray(t)?t.forEach(o):o(t),this}};Me.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(Me.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});y.freezeMethods(Me);function Cs(e,t){const n=this||Pn,s=t||n,r=Me.from(s.headers);let o=s.data;return y.forEach(e,function(l){o=l.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function gi(e){return!!(e&&e.__CANCEL__)}function nn(e,t,n){q.call(this,e??"canceled",q.ERR_CANCELED,t,n),this.name="CanceledError"}y.inherits(nn,q,{__CANCEL__:!0});function yi(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new q("Request failed with status code "+n.status,[q.ERR_BAD_REQUEST,q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function sa(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function ra(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=s[o];i||(i=u),n[r]=a,s[r]=u;let f=o,p=0;for(;f!==r;)p+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const m=c&&u-c;return m?Math.round(p*1e3/m):void 0}}function oa(e,t){let n=0,s=1e3/t,r,o;const i=(u,c=Date.now())=>{n=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const Jn=(e,t,n=3)=>{let s=0;const r=ra(50,250);return oa(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-s,u=r(a),c=i<=l;s=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Wr=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},zr=e=>(...t)=>y.asap(()=>e(...t)),ia=we.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,we.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,la=we.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];y.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),y.isString(s)&&i.push("path="+s),y.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ca(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function aa(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function bi(e,t,n){let s=!ca(t);return e&&(s||n==!1)?aa(e,t):t}const Jr=e=>e instanceof Me?{...e}:e;function Ut(e,t){t=t||{};const n={};function s(u,c,f,p){return y.isPlainObject(u)&&y.isPlainObject(c)?y.merge.call({caseless:p},u,c):y.isPlainObject(c)?y.merge({},c):y.isArray(c)?c.slice():c}function r(u,c,f,p){if(y.isUndefined(c)){if(!y.isUndefined(u))return s(void 0,u,f,p)}else return s(u,c,f,p)}function o(u,c){if(!y.isUndefined(c))return s(void 0,c)}function i(u,c){if(y.isUndefined(c)){if(!y.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function l(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>r(Jr(u),Jr(c),f,!0)};return y.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||r,p=f(e[c],t[c],c);y.isUndefined(p)&&f!==l||(n[c]=p)}),n}const _i=e=>{const t=Ut({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Me.from(i),t.url=hi(bi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(y.isFormData(n)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(we.hasStandardBrowserEnv&&(s&&y.isFunction(s)&&(s=s(t)),s||s!==!1&&ia(t.url))){const u=r&&o&&la.read(o);u&&i.set(r,u)}return t},ua=typeof XMLHttpRequest<"u",fa=ua&&function(e){return new Promise(function(n,s){const r=_i(e);let o=r.data;const i=Me.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=r,c,f,p,m,b;function x(){m&&m(),b&&b(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let S=new XMLHttpRequest;S.open(r.method.toUpperCase(),r.url,!0),S.timeout=r.timeout;function C(){if(!S)return;const I=Me.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),D={data:!l||l==="text"||l==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:I,config:e,request:S};yi(function(J){n(J),x()},function(J){s(J),x()},D),S=null}"onloadend"in S?S.onloadend=C:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(C)},S.onabort=function(){S&&(s(new q("Request aborted",q.ECONNABORTED,e,S)),S=null)},S.onerror=function(){s(new q("Network Error",q.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let L=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const D=r.transitional||pi;r.timeoutErrorMessage&&(L=r.timeoutErrorMessage),s(new q(L,D.clarifyTimeoutError?q.ETIMEDOUT:q.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&y.forEach(i.toJSON(),function(L,D){S.setRequestHeader(D,L)}),y.isUndefined(r.withCredentials)||(S.withCredentials=!!r.withCredentials),l&&l!=="json"&&(S.responseType=r.responseType),u&&([p,b]=Jn(u,!0),S.addEventListener("progress",p)),a&&S.upload&&([f,m]=Jn(a),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",m)),(r.cancelToken||r.signal)&&(c=I=>{S&&(s(!I||I.type?new nn(null,e,S):I),S.abort(),S=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const O=sa(r.url);if(O&&we.protocols.indexOf(O)===-1){s(new q("Unsupported protocol "+O+":",q.ERR_BAD_REQUEST,e));return}S.send(o||null)})},da=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,l();const c=u instanceof Error?u:this.reason;s.abort(c instanceof q?c:new nn(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new q(`timeout ${t} of ms exceeded`,q.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=s;return a.unsubscribe=()=>y.asap(l),a}},ha=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},pa=async function*(e,t){for await(const n of ma(e))yield*ha(n,t)},ma=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Gr=(e,t,n,s)=>{const r=pa(e,t);let o=0,i,l=a=>{i||(i=!0,s&&s(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await r.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let p=o+=f;n(p)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},fs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",vi=fs&&typeof ReadableStream=="function",ga=fs&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),wi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ya=vi&&wi(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Xr=64*1024,zs=vi&&wi(()=>y.isReadableStream(new Response("").body)),Gn={stream:zs&&(e=>e.body)};fs&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Gn[t]&&(Gn[t]=y.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new q(`Response type '${t}' is not supported`,q.ERR_NOT_SUPPORT,s)})})})(new Response);const ba=async e=>{if(e==null)return 0;if(y.isBlob(e))return e.size;if(y.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(y.isArrayBufferView(e)||y.isArrayBuffer(e))return e.byteLength;if(y.isURLSearchParams(e)&&(e=e+""),y.isString(e))return(await ga(e)).byteLength},_a=async(e,t)=>{const n=y.toFiniteNumber(e.getContentLength());return n??ba(t)},va=fs&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:p}=_i(e);u=u?(u+"").toLowerCase():"text";let m=da([r,o&&o.toAbortSignal()],i),b;const x=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let S;try{if(a&&ya&&n!=="get"&&n!=="head"&&(S=await _a(c,s))!==0){let D=new Request(t,{method:"POST",body:s,duplex:"half"}),se;if(y.isFormData(s)&&(se=D.headers.get("content-type"))&&c.setContentType(se),D.body){const[J,V]=Wr(S,Jn(zr(a)));s=Gr(D.body,Xr,J,V)}}y.isString(f)||(f=f?"include":"omit");const C="credentials"in Request.prototype;b=new Request(t,{...p,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:C?f:void 0});let O=await fetch(b);const I=zs&&(u==="stream"||u==="response");if(zs&&(l||I&&x)){const D={};["status","statusText","headers"].forEach(z=>{D[z]=O[z]});const se=y.toFiniteNumber(O.headers.get("content-length")),[J,V]=l&&Wr(se,Jn(zr(l),!0))||[];O=new Response(Gr(O.body,Xr,J,()=>{V&&V(),x&&x()}),D)}u=u||"text";let L=await Gn[y.findKey(Gn,u)||"text"](O,e);return!I&&x&&x(),await new Promise((D,se)=>{yi(D,se,{data:L,headers:Me.from(O.headers),status:O.status,statusText:O.statusText,config:e,request:b})})}catch(C){throw x&&x(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new q("Network Error",q.ERR_NETWORK,e,b),{cause:C.cause||C}):q.from(C,C&&C.code,e,b)}}),Js={http:Mc,xhr:fa,fetch:va};y.forEach(Js,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Qr=e=>`- ${e}`,wa=e=>y.isFunction(e)||e===null||e===!1,xi={getAdapter:e=>{e=y.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!wa(n)&&(s=Js[(i=String(n)).toLowerCase()],s===void 0))throw new q(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Qr).join(`
`):" "+Qr(o[0]):"as no adapter specified";throw new q("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:Js};function Ps(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new nn(null,e)}function Yr(e){return Ps(e),e.headers=Me.from(e.headers),e.data=Cs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xi.getAdapter(e.adapter||Pn.adapter)(e).then(function(s){return Ps(e),s.data=Cs.call(e,e.transformResponse,s),s.headers=Me.from(s.headers),s},function(s){return gi(s)||(Ps(e),s&&s.response&&(s.response.data=Cs.call(e,e.transformResponse,s.response),s.response.headers=Me.from(s.response.headers))),Promise.reject(s)})}const Ei="1.9.0",ds={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ds[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Zr={};ds.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Ei+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new q(r(i," has been removed"+(n?" in "+n:"")),q.ERR_DEPRECATED);return n&&!Zr[i]&&(Zr[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};ds.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function xa(e,t,n){if(typeof e!="object")throw new q("options must be an object",q.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new q("option "+o+" must be "+a,q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new q("Unknown option "+o,q.ERR_BAD_OPTION)}}const Hn={assertOptions:xa,validators:ds},Ze=Hn.validators;let Ft=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Vr,response:new Vr}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ut(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&Hn.assertOptions(s,{silentJSONParsing:Ze.transitional(Ze.boolean),forcedJSONParsing:Ze.transitional(Ze.boolean),clarifyTimeoutError:Ze.transitional(Ze.boolean)},!1),r!=null&&(y.isFunction(r)?n.paramsSerializer={serialize:r}:Hn.assertOptions(r,{encode:Ze.function,serialize:Ze.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Hn.assertOptions(n,{baseUrl:Ze.spelling("baseURL"),withXsrfToken:Ze.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&y.merge(o.common,o[n.method]);o&&y.forEach(["delete","get","head","post","put","patch","common"],b=>{delete o[b]}),n.headers=Me.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(a=a&&x.synchronous,l.unshift(x.fulfilled,x.rejected))});const u=[];this.interceptors.response.forEach(function(x){u.push(x.fulfilled,x.rejected)});let c,f=0,p;if(!a){const b=[Yr.bind(this),void 0];for(b.unshift.apply(b,l),b.push.apply(b,u),p=b.length,c=Promise.resolve(n);f<p;)c=c.then(b[f++],b[f++]);return c}p=l.length;let m=n;for(f=0;f<p;){const b=l[f++],x=l[f++];try{m=b(m)}catch(S){x.call(this,S);break}}try{c=Yr.call(this,m)}catch(b){return Promise.reject(b)}for(f=0,p=u.length;f<p;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=Ut(this.defaults,t);const n=bi(t.baseURL,t.url,t.allowAbsoluteUrls);return hi(n,t.params,t.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(t){Ft.prototype[t]=function(n,s){return this.request(Ut(s||{},{method:t,url:n,data:(s||{}).data}))}});y.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(Ut(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Ft.prototype[t]=n(),Ft.prototype[t+"Form"]=n(!0)});let Ea=class Si{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new nn(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Si(function(r){t=r}),cancel:t}}};function Sa(e){return function(n){return e.apply(null,n)}}function Ra(e){return y.isObject(e)&&e.isAxiosError===!0}const Gs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Gs).forEach(([e,t])=>{Gs[t]=e});function Ri(e){const t=new Ft(e),n=ti(Ft.prototype.request,t);return y.extend(n,Ft.prototype,t,{allOwnKeys:!0}),y.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Ri(Ut(e,r))},n}const ne=Ri(Pn);ne.Axios=Ft;ne.CanceledError=nn;ne.CancelToken=Ea;ne.isCancel=gi;ne.VERSION=Ei;ne.toFormData=us;ne.AxiosError=q;ne.Cancel=ne.CanceledError;ne.all=function(t){return Promise.all(t)};ne.spread=Sa;ne.isAxiosError=Ra;ne.mergeConfig=Ut;ne.AxiosHeaders=Me;ne.formToJSON=e=>mi(y.isHTMLForm(e)?new FormData(e):e);ne.getAdapter=xi.getAdapter;ne.HttpStatusCode=Gs;ne.default=ne;const{Axios:Dh,AxiosError:Uh,CanceledError:Bh,isCancel:$h,CancelToken:Hh,VERSION:qh,all:Vh,Cancel:Kh,isAxiosError:Wh,spread:zh,toFormData:Jh,AxiosHeaders:Gh,HttpStatusCode:Xh,formToJSON:Qh,getAdapter:Yh,mergeConfig:Zh}=ne,Fn=[{id:"1",email:"<EMAIL>",role:"buyer",name:"John Buyer"},{id:"2",email:"<EMAIL>",role:"seller",name:"Jane Seller",businessName:"Kigali Gas Supplies",businessLocation:"Gasabo",businessPhone:"+250 788 123 456"},{id:"3",email:"<EMAIL>",role:"admin",name:"Admin User"}],eo=e=>new Promise(t=>setTimeout(t,e)),to={async login(e,t){if(await eo(1e3),t!=="demo123")throw new Error("Invalid credentials");const n=Fn.find(r=>r.email===e);if(!n)throw new Error("User not found");const s=`mock-jwt-token-${n.id}-${Date.now()}`;return{user:n,token:s}},async register(e){if(await eo(1e3),Fn.find(r=>r.email===e.email))throw new Error("User already exists");const n={id:`${Fn.length+1}`,email:e.email,role:e.role,...e.name&&{name:e.name},...e.businessName&&{businessName:e.businessName},...e.businessLocation&&{businessLocation:e.businessLocation},...e.businessPhone&&{businessPhone:e.businessPhone}};Fn.push(n);const s=`mock-jwt-token-${n.id}-${Date.now()}`;return{user:n,token:s}}};ne.interceptors.request.use(async e=>{const t=e.url||"";if(t.includes("/api/auth/login")){const{email:n,password:s}=e.data;try{const o={data:await to.login(n,s),status:200,statusText:"OK",headers:{},config:e};return Promise.reject({response:o,isAxiosError:!0,config:e,request:{},message:"Mock response"})}catch(r){return Promise.reject({response:{data:{message:r.message},status:401,statusText:"Unauthorized",headers:{},config:e},isAxiosError:!0,config:e,request:{},message:r.message})}}if(t.includes("/api/auth/register"))try{const s={data:await to.register(e.data),status:201,statusText:"Created",headers:{},config:e};return Promise.reject({response:s,isAxiosError:!0,config:e,request:{},message:"Mock response"})}catch(n){return Promise.reject({response:{data:{message:n.message},status:400,statusText:"Bad Request",headers:{},config:e},isAxiosError:!0,config:e,request:{},message:n.message})}return e});ne.interceptors.response.use(e=>e,e=>e.message==="Mock response"&&e.response?Promise.resolve(e.response):Promise.reject(e));/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function hr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const oe={},Jt=[],st=()=>{},Aa=()=>!1,hs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),pr=e=>e.startsWith("onUpdate:"),xe=Object.assign,mr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Oa=Object.prototype.hasOwnProperty,ee=(e,t)=>Oa.call(e,t),B=Array.isArray,Gt=e=>In(e)==="[object Map]",sn=e=>In(e)==="[object Set]",no=e=>In(e)==="[object Date]",H=e=>typeof e=="function",me=e=>typeof e=="string",rt=e=>typeof e=="symbol",ae=e=>e!==null&&typeof e=="object",Ai=e=>(ae(e)||H(e))&&H(e.then)&&H(e.catch),Oi=Object.prototype.toString,In=e=>Oi.call(e),Ta=e=>In(e).slice(8,-1),Ti=e=>In(e)==="[object Object]",gr=e=>me(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,fn=hr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ps=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ca=/-(\w)/g,Ke=ps(e=>e.replace(Ca,(t,n)=>n?n.toUpperCase():"")),Pa=/\B([A-Z])/g,$t=ps(e=>e.replace(Pa,"-$1").toLowerCase()),ms=ps(e=>e.charAt(0).toUpperCase()+e.slice(1)),Is=ps(e=>e?`on${ms(e)}`:""),Rt=(e,t)=>!Object.is(e,t),qn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ci=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Xn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let so;const gs=()=>so||(so=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function yr(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=me(s)?La(s):yr(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(me(e)||ae(e))return e}const Ia=/;(?![^(]*\))/g,ka=/:([^]+)/,Na=/\/\*[^]*?\*\//g;function La(e){const t={};return e.replace(Na,"").split(Ia).forEach(n=>{if(n){const s=n.split(ka);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function br(e){let t="";if(me(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=br(e[n]);s&&(t+=s+" ")}else if(ae(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ma="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Fa=hr(Ma);function Pi(e){return!!e||e===""}function ja(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Bt(e[s],t[s]);return n}function Bt(e,t){if(e===t)return!0;let n=no(e),s=no(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=rt(e),s=rt(t),n||s)return e===t;if(n=B(e),s=B(t),n||s)return n&&s?ja(e,t):!1;if(n=ae(e),s=ae(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Bt(e[i],t[i]))return!1}}return String(e)===String(t)}function _r(e,t){return e.findIndex(n=>Bt(n,t))}const Ii=e=>!!(e&&e.__v_isRef===!0),vt=e=>me(e)?e:e==null?"":B(e)||ae(e)&&(e.toString===Oi||!H(e.toString))?Ii(e)?vt(e.value):JSON.stringify(e,ki,2):String(e),ki=(e,t)=>Ii(t)?ki(e,t.value):Gt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[ks(s,o)+" =>"]=r,n),{})}:sn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ks(n))}:rt(t)?ks(t):ae(t)&&!B(t)&&!Ti(t)?String(t):t,ks=(e,t="")=>{var n;return rt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let _e;class Ni{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=_e,!t&&_e&&(this.index=(_e.scopes||(_e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=_e;try{return _e=this,t()}finally{_e=n}}}on(){++this._on===1&&(this.prevScope=_e,_e=this)}off(){this._on>0&&--this._on===0&&(_e=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Li(e){return new Ni(e)}function Mi(){return _e}function Da(e,t=!1){_e&&_e.cleanups.push(e)}let ce;const Ns=new WeakSet;class Fi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,_e&&_e.active&&_e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Ns.has(this)&&(Ns.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Di(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ro(this),Ui(this);const t=ce,n=We;ce=this,We=!0;try{return this.fn()}finally{Bi(this),ce=t,We=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)xr(t);this.deps=this.depsTail=void 0,ro(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Ns.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Xs(this)&&this.run()}get dirty(){return Xs(this)}}let ji=0,dn,hn;function Di(e,t=!1){if(e.flags|=8,t){e.next=hn,hn=e;return}e.next=dn,dn=e}function vr(){ji++}function wr(){if(--ji>0)return;if(hn){let t=hn;for(hn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;dn;){let t=dn;for(dn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Ui(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Bi(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),xr(s),Ua(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Xs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&($i(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function $i(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===xn)||(e.globalVersion=xn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Xs(e))))return;e.flags|=2;const t=e.dep,n=ce,s=We;ce=e,We=!0;try{Ui(e);const r=e.fn(e._value);(t.version===0||Rt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ce=n,We=s,Bi(e),e.flags&=-3}}function xr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)xr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ua(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let We=!0;const Hi=[];function ht(){Hi.push(We),We=!1}function pt(){const e=Hi.pop();We=e===void 0?!0:e}function ro(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ce;ce=void 0;try{t()}finally{ce=n}}}let xn=0;class Ba{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Er{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ce||!We||ce===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ce)n=this.activeLink=new Ba(ce,this),ce.deps?(n.prevDep=ce.depsTail,ce.depsTail.nextDep=n,ce.depsTail=n):ce.deps=ce.depsTail=n,qi(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ce.depsTail,n.nextDep=void 0,ce.depsTail.nextDep=n,ce.depsTail=n,ce.deps===n&&(ce.deps=s)}return n}trigger(t){this.version++,xn++,this.notify(t)}notify(t){vr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{wr()}}}function qi(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)qi(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Qn=new WeakMap,jt=Symbol(""),Qs=Symbol(""),En=Symbol("");function ve(e,t,n){if(We&&ce){let s=Qn.get(e);s||Qn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Er),r.map=s,r.key=n),r.track()}}function ut(e,t,n,s,r,o){const i=Qn.get(e);if(!i){xn++;return}const l=a=>{a&&a.trigger()};if(vr(),t==="clear")i.forEach(l);else{const a=B(e),u=a&&gr(n);if(a&&n==="length"){const c=Number(s);i.forEach((f,p)=>{(p==="length"||p===En||!rt(p)&&p>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(En)),t){case"add":a?u&&l(i.get("length")):(l(i.get(jt)),Gt(e)&&l(i.get(Qs)));break;case"delete":a||(l(i.get(jt)),Gt(e)&&l(i.get(Qs)));break;case"set":Gt(e)&&l(i.get(jt));break}}wr()}function $a(e,t){const n=Qn.get(e);return n&&n.get(t)}function Vt(e){const t=Q(e);return t===e?t:(ve(t,"iterate",En),He(e)?t:t.map(ye))}function ys(e){return ve(e=Q(e),"iterate",En),e}const Ha={__proto__:null,[Symbol.iterator](){return Ls(this,Symbol.iterator,ye)},concat(...e){return Vt(this).concat(...e.map(t=>B(t)?Vt(t):t))},entries(){return Ls(this,"entries",e=>(e[1]=ye(e[1]),e))},every(e,t){return lt(this,"every",e,t,void 0,arguments)},filter(e,t){return lt(this,"filter",e,t,n=>n.map(ye),arguments)},find(e,t){return lt(this,"find",e,t,ye,arguments)},findIndex(e,t){return lt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return lt(this,"findLast",e,t,ye,arguments)},findLastIndex(e,t){return lt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return lt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ms(this,"includes",e)},indexOf(...e){return Ms(this,"indexOf",e)},join(e){return Vt(this).join(e)},lastIndexOf(...e){return Ms(this,"lastIndexOf",e)},map(e,t){return lt(this,"map",e,t,void 0,arguments)},pop(){return ln(this,"pop")},push(...e){return ln(this,"push",e)},reduce(e,...t){return oo(this,"reduce",e,t)},reduceRight(e,...t){return oo(this,"reduceRight",e,t)},shift(){return ln(this,"shift")},some(e,t){return lt(this,"some",e,t,void 0,arguments)},splice(...e){return ln(this,"splice",e)},toReversed(){return Vt(this).toReversed()},toSorted(e){return Vt(this).toSorted(e)},toSpliced(...e){return Vt(this).toSpliced(...e)},unshift(...e){return ln(this,"unshift",e)},values(){return Ls(this,"values",ye)}};function Ls(e,t,n){const s=ys(e),r=s[t]();return s!==e&&!He(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const qa=Array.prototype;function lt(e,t,n,s,r,o){const i=ys(e),l=i!==e&&!He(e),a=i[t];if(a!==qa[t]){const f=a.apply(e,o);return l?ye(f):f}let u=n;i!==e&&(l?u=function(f,p){return n.call(this,ye(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const c=a.call(i,u,s);return l&&r?r(c):c}function oo(e,t,n,s){const r=ys(e);let o=n;return r!==e&&(He(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,ye(l),a,e)}),r[t](o,...s)}function Ms(e,t,n){const s=Q(e);ve(s,"iterate",En);const r=s[t](...n);return(r===-1||r===!1)&&Ar(n[0])?(n[0]=Q(n[0]),s[t](...n)):r}function ln(e,t,n=[]){ht(),vr();const s=Q(e)[t].apply(e,n);return wr(),pt(),s}const Va=hr("__proto__,__v_isRef,__isVue"),Vi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(rt));function Ka(e){rt(e)||(e=String(e));const t=Q(this);return ve(t,"has",e),t.hasOwnProperty(e)}class Ki{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?tu:Gi:o?Ji:zi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!r){let a;if(i&&(a=Ha[n]))return a;if(n==="hasOwnProperty")return Ka}const l=Reflect.get(t,n,pe(t)?t:s);return(rt(n)?Vi.has(n):Va(n))||(r||ve(t,"get",n),o)?l:pe(l)?i&&gr(n)?l:l.value:ae(l)?r?Qi(l):kn(l):l}}class Wi extends Ki{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const a=Tt(o);if(!He(s)&&!Tt(s)&&(o=Q(o),s=Q(s)),!B(t)&&pe(o)&&!pe(s))return a?!1:(o.value=s,!0)}const i=B(t)&&gr(n)?Number(n)<t.length:ee(t,n),l=Reflect.set(t,n,s,pe(t)?t:r);return t===Q(r)&&(i?Rt(s,o)&&ut(t,"set",n,s):ut(t,"add",n,s)),l}deleteProperty(t,n){const s=ee(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ut(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!rt(n)||!Vi.has(n))&&ve(t,"has",n),s}ownKeys(t){return ve(t,"iterate",B(t)?"length":jt),Reflect.ownKeys(t)}}class Wa extends Ki{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const za=new Wi,Ja=new Wa,Ga=new Wi(!0);const Ys=e=>e,jn=e=>Reflect.getPrototypeOf(e);function Xa(e,t,n){return function(...s){const r=this.__v_raw,o=Q(r),i=Gt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=r[e](...s),c=n?Ys:t?Yn:ye;return!t&&ve(o,"iterate",a?Qs:jt),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:l?[c(f[0]),c(f[1])]:c(f),done:p}},[Symbol.iterator](){return this}}}}function Dn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Qa(e,t){const n={get(r){const o=this.__v_raw,i=Q(o),l=Q(r);e||(Rt(r,l)&&ve(i,"get",r),ve(i,"get",l));const{has:a}=jn(i),u=t?Ys:e?Yn:ye;if(a.call(i,r))return u(o.get(r));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ve(Q(r),"iterate",jt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Q(o),l=Q(r);return e||(Rt(r,l)&&ve(i,"has",r),ve(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=Q(l),u=t?Ys:e?Yn:ye;return!e&&ve(a,"iterate",jt),l.forEach((c,f)=>r.call(o,u(c),u(f),i))}};return xe(n,e?{add:Dn("add"),set:Dn("set"),delete:Dn("delete"),clear:Dn("clear")}:{add(r){!t&&!He(r)&&!Tt(r)&&(r=Q(r));const o=Q(this);return jn(o).has.call(o,r)||(o.add(r),ut(o,"add",r,r)),this},set(r,o){!t&&!He(o)&&!Tt(o)&&(o=Q(o));const i=Q(this),{has:l,get:a}=jn(i);let u=l.call(i,r);u||(r=Q(r),u=l.call(i,r));const c=a.call(i,r);return i.set(r,o),u?Rt(o,c)&&ut(i,"set",r,o):ut(i,"add",r,o),this},delete(r){const o=Q(this),{has:i,get:l}=jn(o);let a=i.call(o,r);a||(r=Q(r),a=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return a&&ut(o,"delete",r,void 0),u},clear(){const r=Q(this),o=r.size!==0,i=r.clear();return o&&ut(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Xa(r,e,t)}),n}function Sr(e,t){const n=Qa(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(ee(n,r)&&r in s?n:s,r,o)}const Ya={get:Sr(!1,!1)},Za={get:Sr(!1,!0)},eu={get:Sr(!0,!1)};const zi=new WeakMap,Ji=new WeakMap,Gi=new WeakMap,tu=new WeakMap;function nu(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function su(e){return e.__v_skip||!Object.isExtensible(e)?0:nu(Ta(e))}function kn(e){return Tt(e)?e:Rr(e,!1,za,Ya,zi)}function Xi(e){return Rr(e,!1,Ga,Za,Ji)}function Qi(e){return Rr(e,!0,Ja,eu,Gi)}function Rr(e,t,n,s,r){if(!ae(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=su(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function At(e){return Tt(e)?At(e.__v_raw):!!(e&&e.__v_isReactive)}function Tt(e){return!!(e&&e.__v_isReadonly)}function He(e){return!!(e&&e.__v_isShallow)}function Ar(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function Or(e){return!ee(e,"__v_skip")&&Object.isExtensible(e)&&Ci(e,"__v_skip",!0),e}const ye=e=>ae(e)?kn(e):e,Yn=e=>ae(e)?Qi(e):e;function pe(e){return e?e.__v_isRef===!0:!1}function Ot(e){return Yi(e,!1)}function ru(e){return Yi(e,!0)}function Yi(e,t){return pe(e)?e:new ou(e,t)}class ou{constructor(t,n){this.dep=new Er,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Q(t),this._value=n?t:ye(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||He(t)||Tt(t);t=s?t:Q(t),Rt(t,n)&&(this._rawValue=t,this._value=s?t:ye(t),this.dep.trigger())}}function he(e){return pe(e)?e.value:e}const iu={get:(e,t,n)=>t==="__v_raw"?e:he(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return pe(r)&&!pe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Zi(e){return At(e)?e:new Proxy(e,iu)}function lu(e){const t=B(e)?new Array(e.length):{};for(const n in e)t[n]=au(e,n);return t}class cu{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return $a(Q(this._object),this._key)}}function au(e,t,n){const s=e[t];return pe(s)?s:new cu(e,t,n)}class uu{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Er(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=xn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ce!==this)return Di(this,!0),!0}get value(){const t=this.dep.track();return $i(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function fu(e,t,n=!1){let s,r;return H(e)?s=e:(s=e.get,r=e.set),new uu(s,r,n)}const Un={},Zn=new WeakMap;let Lt;function du(e,t=!1,n=Lt){if(n){let s=Zn.get(n);s||Zn.set(n,s=[]),s.push(e)}}function hu(e,t,n=oe){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=n,u=L=>r?L:He(L)||r===!1||r===0?ft(L,1):ft(L);let c,f,p,m,b=!1,x=!1;if(pe(e)?(f=()=>e.value,b=He(e)):At(e)?(f=()=>u(e),b=!0):B(e)?(x=!0,b=e.some(L=>At(L)||He(L)),f=()=>e.map(L=>{if(pe(L))return L.value;if(At(L))return u(L);if(H(L))return a?a(L,2):L()})):H(e)?t?f=a?()=>a(e,2):e:f=()=>{if(p){ht();try{p()}finally{pt()}}const L=Lt;Lt=c;try{return a?a(e,3,[m]):e(m)}finally{Lt=L}}:f=st,t&&r){const L=f,D=r===!0?1/0:r;f=()=>ft(L(),D)}const S=Mi(),C=()=>{c.stop(),S&&S.active&&mr(S.effects,c)};if(o&&t){const L=t;t=(...D)=>{L(...D),C()}}let O=x?new Array(e.length).fill(Un):Un;const I=L=>{if(!(!(c.flags&1)||!c.dirty&&!L))if(t){const D=c.run();if(r||b||(x?D.some((se,J)=>Rt(se,O[J])):Rt(D,O))){p&&p();const se=Lt;Lt=c;try{const J=[D,O===Un?void 0:x&&O[0]===Un?[]:O,m];O=D,a?a(t,3,J):t(...J)}finally{Lt=se}}}else c.run()};return l&&l(I),c=new Fi(f),c.scheduler=i?()=>i(I,!1):I,m=L=>du(L,!1,c),p=c.onStop=()=>{const L=Zn.get(c);if(L){if(a)a(L,4);else for(const D of L)D();Zn.delete(c)}},t?s?I(!0):O=c.run():i?i(I.bind(null,!0),!0):c.run(),C.pause=c.pause.bind(c),C.resume=c.resume.bind(c),C.stop=C,C}function ft(e,t=1/0,n){if(t<=0||!ae(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,pe(e))ft(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)ft(e[s],t,n);else if(sn(e)||Gt(e))e.forEach(s=>{ft(s,t,n)});else if(Ti(e)){for(const s in e)ft(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ft(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Nn(e,t,n,s){try{return s?e(...s):e()}catch(r){bs(r,t,n)}}function ot(e,t,n,s){if(H(e)){const r=Nn(e,t,n,s);return r&&Ai(r)&&r.catch(o=>{bs(o,t,n)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(ot(e[o],t,n,s));return r}}function bs(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){ht(),Nn(o,null,10,[e,a,u]),pt();return}}pu(e,n,r,s,i)}function pu(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Oe=[];let tt=-1;const Xt=[];let xt=null,Wt=0;const el=Promise.resolve();let es=null;function _s(e){const t=es||el;return e?t.then(this?e.bind(this):e):t}function mu(e){let t=tt+1,n=Oe.length;for(;t<n;){const s=t+n>>>1,r=Oe[s],o=Sn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Tr(e){if(!(e.flags&1)){const t=Sn(e),n=Oe[Oe.length-1];!n||!(e.flags&2)&&t>=Sn(n)?Oe.push(e):Oe.splice(mu(t),0,e),e.flags|=1,tl()}}function tl(){es||(es=el.then(sl))}function gu(e){B(e)?Xt.push(...e):xt&&e.id===-1?xt.splice(Wt+1,0,e):e.flags&1||(Xt.push(e),e.flags|=1),tl()}function io(e,t,n=tt+1){for(;n<Oe.length;n++){const s=Oe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Oe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function nl(e){if(Xt.length){const t=[...new Set(Xt)].sort((n,s)=>Sn(n)-Sn(s));if(Xt.length=0,xt){xt.push(...t);return}for(xt=t,Wt=0;Wt<xt.length;Wt++){const n=xt[Wt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}xt=null,Wt=0}}const Sn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function sl(e){try{for(tt=0;tt<Oe.length;tt++){const t=Oe[tt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Nn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;tt<Oe.length;tt++){const t=Oe[tt];t&&(t.flags&=-2)}tt=-1,Oe.length=0,nl(),es=null,(Oe.length||Xt.length)&&sl()}}let Ne=null,rl=null;function ts(e){const t=Ne;return Ne=e,rl=e&&e.type.__scopeId||null,t}function le(e,t=Ne,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&yo(-1);const o=ts(t);let i;try{i=e(...r)}finally{ts(o),s._d&&yo(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function ep(e,t){if(Ne===null)return e;const n=Ss(Ne),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=oe]=t[r];o&&(H(o)&&(o={mounted:o,updated:o}),o.deep&&ft(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function kt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[s];a&&(ht(),ot(a,n,8,[e.el,l,e,t]),pt())}}const yu=Symbol("_vte"),bu=e=>e.__isTeleport;function Cr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Cr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function vs(e,t){return H(e)?xe({name:e.name},t,{setup:e}):e}function ol(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ns(e,t,n,s,r=!1){if(B(e)){e.forEach((b,x)=>ns(b,t&&(B(t)?t[x]:t),n,s,r));return}if(pn(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&ns(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Ss(s.component):s.el,i=r?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===oe?l.refs={}:l.refs,f=l.setupState,p=Q(f),m=f===oe?()=>!1:b=>ee(p,b);if(u!=null&&u!==a&&(me(u)?(c[u]=null,m(u)&&(f[u]=null)):pe(u)&&(u.value=null)),H(a))Nn(a,l,12,[i,c]);else{const b=me(a),x=pe(a);if(b||x){const S=()=>{if(e.f){const C=b?m(a)?f[a]:c[a]:a.value;r?B(C)&&mr(C,o):B(C)?C.includes(o)||C.push(o):b?(c[a]=[o],m(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else b?(c[a]=i,m(a)&&(f[a]=i)):x&&(a.value=i,e.k&&(c[e.k]=i))};i?(S.id=-1,Ue(S,n)):S()}}}gs().requestIdleCallback;gs().cancelIdleCallback;const pn=e=>!!e.type.__asyncLoader,il=e=>e.type.__isKeepAlive;function _u(e,t){ll(e,"a",t)}function vu(e,t){ll(e,"da",t)}function ll(e,t,n=be){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(ws(t,s,n),n){let r=n.parent;for(;r&&r.parent;)il(r.parent.vnode)&&wu(s,t,n,r),r=r.parent}}function wu(e,t,n,s){const r=ws(t,e,s,!0);Ir(()=>{mr(s[t],r)},n)}function ws(e,t,n=be,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ht();const l=Ln(n),a=ot(t,n,e,i);return l(),pt(),a});return s?r.unshift(o):r.push(o),o}}const mt=e=>(t,n=be)=>{(!An||e==="sp")&&ws(e,(...s)=>t(...s),n)},xu=mt("bm"),Pr=mt("m"),Eu=mt("bu"),Su=mt("u"),Ru=mt("bum"),Ir=mt("um"),Au=mt("sp"),Ou=mt("rtg"),Tu=mt("rtc");function Cu(e,t=be){ws("ec",e,t)}const Pu="components";function Iu(e,t){return Nu(Pu,e,!0,t)||e}const ku=Symbol.for("v-ndc");function Nu(e,t,n=!0,s=!1){const r=Ne||be;if(r){const o=r.type;{const l=xf(o,!1);if(l&&(l===t||l===Ke(t)||l===ms(Ke(t))))return o}const i=lo(r[e]||o[e],t)||lo(r.appContext[e],t);return!i&&s?o:i}}function lo(e,t){return e&&(e[t]||e[Ke(t)]||e[ms(Ke(t))])}function tp(e,t,n,s){let r;const o=n,i=B(e);if(i||me(e)){const l=i&&At(e);let a=!1,u=!1;l&&(a=!He(e),u=Tt(e),e=ys(e)),r=new Array(e.length);for(let c=0,f=e.length;c<f;c++)r[c]=t(a?u?Yn(ye(e[c])):ye(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ae(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}const Zs=e=>e?Ol(e)?Ss(e):Zs(e.parent):null,mn=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Zs(e.parent),$root:e=>Zs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>al(e),$forceUpdate:e=>e.f||(e.f=()=>{Tr(e.update)}),$nextTick:e=>e.n||(e.n=_s.bind(e.proxy)),$watch:e=>tf.bind(e)}),Fs=(e,t)=>e!==oe&&!e.__isScriptSetup&&ee(e,t),Lu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Fs(s,t))return i[t]=1,s[t];if(r!==oe&&ee(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&ee(u,t))return i[t]=3,o[t];if(n!==oe&&ee(n,t))return i[t]=4,n[t];er&&(i[t]=0)}}const c=mn[t];let f,p;if(c)return t==="$attrs"&&ve(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==oe&&ee(n,t))return i[t]=4,n[t];if(p=a.config.globalProperties,ee(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Fs(r,t)?(r[t]=n,!0):s!==oe&&ee(s,t)?(s[t]=n,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==oe&&ee(e,i)||Fs(t,i)||(l=o[0])&&ee(l,i)||ee(s,i)||ee(mn,i)||ee(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ee(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function co(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let er=!0;function Mu(e){const t=al(e),n=e.proxy,s=e.ctx;er=!1,t.beforeCreate&&ao(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:p,beforeUpdate:m,updated:b,activated:x,deactivated:S,beforeDestroy:C,beforeUnmount:O,destroyed:I,unmounted:L,render:D,renderTracked:se,renderTriggered:J,errorCaptured:V,serverPrefetch:z,expose:de,inheritAttrs:Ee,components:Fe,directives:Ce,filters:It}=t;if(u&&Fu(u,s,null),i)for(const K in i){const Y=i[K];H(Y)&&(s[K]=Y.bind(n))}if(r){const K=r.call(n,n);ae(K)&&(e.data=kn(K))}if(er=!0,o)for(const K in o){const Y=o[K],it=H(Y)?Y.bind(n,n):H(Y.get)?Y.get.bind(n,n):st,yt=!H(Y)&&H(Y.set)?Y.set.bind(n):st,Ge=Te({get:it,set:yt});Object.defineProperty(s,K,{enumerable:!0,configurable:!0,get:()=>Ge.value,set:Pe=>Ge.value=Pe})}if(l)for(const K in l)cl(l[K],s,n,K);if(a){const K=H(a)?a.call(n):a;Reflect.ownKeys(K).forEach(Y=>{Vn(Y,K[Y])})}c&&ao(c,e,"c");function fe(K,Y){B(Y)?Y.forEach(it=>K(it.bind(n))):Y&&K(Y.bind(n))}if(fe(xu,f),fe(Pr,p),fe(Eu,m),fe(Su,b),fe(_u,x),fe(vu,S),fe(Cu,V),fe(Tu,se),fe(Ou,J),fe(Ru,O),fe(Ir,L),fe(Au,z),B(de))if(de.length){const K=e.exposed||(e.exposed={});de.forEach(Y=>{Object.defineProperty(K,Y,{get:()=>n[Y],set:it=>n[Y]=it})})}else e.exposed||(e.exposed={});D&&e.render===st&&(e.render=D),Ee!=null&&(e.inheritAttrs=Ee),Fe&&(e.components=Fe),Ce&&(e.directives=Ce),z&&ol(e)}function Fu(e,t,n=st){B(e)&&(e=tr(e));for(const s in e){const r=e[s];let o;ae(r)?"default"in r?o=qe(r.from||s,r.default,!0):o=qe(r.from||s):o=qe(r),pe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function ao(e,t,n){ot(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function cl(e,t,n,s){let r=s.includes(".")?xl(n,s):()=>n[s];if(me(e)){const o=t[e];H(o)&&gn(r,o)}else if(H(e))gn(r,e.bind(n));else if(ae(e))if(B(e))e.forEach(o=>cl(o,t,n,s));else{const o=H(e.handler)?e.handler.bind(n):t[e.handler];H(o)&&gn(r,o,e)}}function al(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(u=>ss(a,u,i,!0)),ss(a,t,i)),ae(t)&&o.set(t,a),a}function ss(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&ss(e,o,n,!0),r&&r.forEach(i=>ss(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=ju[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const ju={data:uo,props:fo,emits:fo,methods:un,computed:un,beforeCreate:Re,created:Re,beforeMount:Re,mounted:Re,beforeUpdate:Re,updated:Re,beforeDestroy:Re,beforeUnmount:Re,destroyed:Re,unmounted:Re,activated:Re,deactivated:Re,errorCaptured:Re,serverPrefetch:Re,components:un,directives:un,watch:Uu,provide:uo,inject:Du};function uo(e,t){return t?e?function(){return xe(H(e)?e.call(this,this):e,H(t)?t.call(this,this):t)}:t:e}function Du(e,t){return un(tr(e),tr(t))}function tr(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Re(e,t){return e?[...new Set([].concat(e,t))]:t}function un(e,t){return e?xe(Object.create(null),e,t):t}function fo(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:xe(Object.create(null),co(e),co(t??{})):t}function Uu(e,t){if(!e)return t;if(!t)return e;const n=xe(Object.create(null),e);for(const s in t)n[s]=Re(e[s],t[s]);return n}function ul(){return{app:null,config:{isNativeTag:Aa,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Bu=0;function $u(e,t){return function(s,r=null){H(s)||(s=xe({},s)),r!=null&&!ae(r)&&(r=null);const o=ul(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:Bu++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Sf,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&H(c.install)?(i.add(c),c.install(u,...f)):H(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,p){if(!a){const m=u._ceVNode||W(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,c,p),a=!0,u._container=c,c.__vue_app__=u,Ss(m.component)}},onUnmount(c){l.push(c)},unmount(){a&&(ot(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=Dt;Dt=u;try{return c()}finally{Dt=f}}};return u}}let Dt=null;function Vn(e,t){if(be){let n=be.provides;const s=be.parent&&be.parent.provides;s===n&&(n=be.provides=Object.create(s)),n[e]=t}}function qe(e,t,n=!1){const s=be||Ne;if(s||Dt){let r=Dt?Dt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&H(t)?t.call(s&&s.proxy):t}}function Hu(){return!!(be||Ne||Dt)}const fl={},dl=()=>Object.create(fl),hl=e=>Object.getPrototypeOf(e)===fl;function qu(e,t,n,s=!1){const r={},o=dl();e.propsDefaults=Object.create(null),pl(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Xi(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Vu(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=Q(r),[a]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let p=c[f];if(xs(e.emitsOptions,p))continue;const m=t[p];if(a)if(ee(o,p))m!==o[p]&&(o[p]=m,u=!0);else{const b=Ke(p);r[b]=nr(a,l,b,m,e,!1)}else m!==o[p]&&(o[p]=m,u=!0)}}}else{pl(e,t,r,o)&&(u=!0);let c;for(const f in l)(!t||!ee(t,f)&&((c=$t(f))===f||!ee(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(r[f]=nr(a,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!ee(t,f))&&(delete o[f],u=!0)}u&&ut(e.attrs,"set","")}function pl(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(fn(a))continue;const u=t[a];let c;r&&ee(r,c=Ke(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:xs(e.emitsOptions,a)||(!(a in s)||u!==s[a])&&(s[a]=u,i=!0)}if(o){const a=Q(n),u=l||oe;for(let c=0;c<o.length;c++){const f=o[c];n[f]=nr(r,a,f,u[f],e,!ee(u,f))}}return i}function nr(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=ee(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&H(a)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const c=Ln(r);s=u[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===$t(n))&&(s=!0))}return s}const Ku=new WeakMap;function ml(e,t,n=!1){const s=n?Ku:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!H(e)){const c=f=>{a=!0;const[p,m]=ml(f,t,!0);xe(i,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ae(e)&&s.set(e,Jt),Jt;if(B(o))for(let c=0;c<o.length;c++){const f=Ke(o[c]);ho(f)&&(i[f]=oe)}else if(o)for(const c in o){const f=Ke(c);if(ho(f)){const p=o[c],m=i[f]=B(p)||H(p)?{type:p}:xe({},p),b=m.type;let x=!1,S=!0;if(B(b))for(let C=0;C<b.length;++C){const O=b[C],I=H(O)&&O.name;if(I==="Boolean"){x=!0;break}else I==="String"&&(S=!1)}else x=H(b)&&b.name==="Boolean";m[0]=x,m[1]=S,(x||ee(m,"default"))&&l.push(f)}}const u=[i,l];return ae(e)&&s.set(e,u),u}function ho(e){return e[0]!=="$"&&!fn(e)}const kr=e=>e[0]==="_"||e==="$stable",Nr=e=>B(e)?e.map(nt):[nt(e)],Wu=(e,t,n)=>{if(t._n)return t;const s=le((...r)=>Nr(t(...r)),n);return s._c=!1,s},gl=(e,t,n)=>{const s=e._ctx;for(const r in e){if(kr(r))continue;const o=e[r];if(H(o))t[r]=Wu(r,o,s);else if(o!=null){const i=Nr(o);t[r]=()=>i}}},yl=(e,t)=>{const n=Nr(t);e.slots.default=()=>n},bl=(e,t,n)=>{for(const s in t)(n||!kr(s))&&(e[s]=t[s])},zu=(e,t,n)=>{const s=e.slots=dl();if(e.vnode.shapeFlag&32){const r=t._;r?(bl(s,t,n),n&&Ci(s,"_",r,!0)):gl(t,s)}else t&&yl(e,t)},Ju=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=oe;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:bl(r,t,n):(o=!t.$stable,gl(t,r)),i=t}else t&&(yl(e,t),i={default:1});if(o)for(const l in r)!kr(l)&&i[l]==null&&delete r[l]},Ue=af;function Gu(e){return Xu(e)}function Xu(e,t){const n=gs();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:p,setScopeId:m=st,insertStaticContent:b}=e,x=(d,h,g,_=null,E=null,w=null,P=void 0,T=null,A=!!h.dynamicChildren)=>{if(d===h)return;d&&!cn(d,h)&&(_=v(d),Pe(d,E,w,!0),d=null),h.patchFlag===-2&&(A=!1,h.dynamicChildren=null);const{type:R,ref:U,shapeFlag:N}=h;switch(R){case Es:S(d,h,g,_);break;case Ct:C(d,h,g,_);break;case Kn:d==null&&O(h,g,_,P);break;case ge:Fe(d,h,g,_,E,w,P,T,A);break;default:N&1?D(d,h,g,_,E,w,P,T,A):N&6?Ce(d,h,g,_,E,w,P,T,A):(N&64||N&128)&&R.process(d,h,g,_,E,w,P,T,A,F)}U!=null&&E&&ns(U,d&&d.ref,w,h||d,!h)},S=(d,h,g,_)=>{if(d==null)s(h.el=l(h.children),g,_);else{const E=h.el=d.el;h.children!==d.children&&u(E,h.children)}},C=(d,h,g,_)=>{d==null?s(h.el=a(h.children||""),g,_):h.el=d.el},O=(d,h,g,_)=>{[d.el,d.anchor]=b(d.children,h,g,_,d.el,d.anchor)},I=({el:d,anchor:h},g,_)=>{let E;for(;d&&d!==h;)E=p(d),s(d,g,_),d=E;s(h,g,_)},L=({el:d,anchor:h})=>{let g;for(;d&&d!==h;)g=p(d),r(d),d=g;r(h)},D=(d,h,g,_,E,w,P,T,A)=>{h.type==="svg"?P="svg":h.type==="math"&&(P="mathml"),d==null?se(h,g,_,E,w,P,T,A):z(d,h,E,w,P,T,A)},se=(d,h,g,_,E,w,P,T)=>{let A,R;const{props:U,shapeFlag:N,transition:j,dirs:$}=d;if(A=d.el=i(d.type,w,U&&U.is,U),N&8?c(A,d.children):N&16&&V(d.children,A,null,_,E,js(d,w),P,T),$&&kt(d,null,_,"created"),J(A,d,d.scopeId,P,_),U){for(const ie in U)ie!=="value"&&!fn(ie)&&o(A,ie,null,U[ie],w,_);"value"in U&&o(A,"value",null,U.value,w),(R=U.onVnodeBeforeMount)&&et(R,_,d)}$&&kt(d,null,_,"beforeMount");const G=Qu(E,j);G&&j.beforeEnter(A),s(A,h,g),((R=U&&U.onVnodeMounted)||G||$)&&Ue(()=>{R&&et(R,_,d),G&&j.enter(A),$&&kt(d,null,_,"mounted")},E)},J=(d,h,g,_,E)=>{if(g&&m(d,g),_)for(let w=0;w<_.length;w++)m(d,_[w]);if(E){let w=E.subTree;if(h===w||Sl(w.type)&&(w.ssContent===h||w.ssFallback===h)){const P=E.vnode;J(d,P,P.scopeId,P.slotScopeIds,E.parent)}}},V=(d,h,g,_,E,w,P,T,A=0)=>{for(let R=A;R<d.length;R++){const U=d[R]=T?Et(d[R]):nt(d[R]);x(null,U,h,g,_,E,w,P,T)}},z=(d,h,g,_,E,w,P)=>{const T=h.el=d.el;let{patchFlag:A,dynamicChildren:R,dirs:U}=h;A|=d.patchFlag&16;const N=d.props||oe,j=h.props||oe;let $;if(g&&Nt(g,!1),($=j.onVnodeBeforeUpdate)&&et($,g,h,d),U&&kt(h,d,g,"beforeUpdate"),g&&Nt(g,!0),(N.innerHTML&&j.innerHTML==null||N.textContent&&j.textContent==null)&&c(T,""),R?de(d.dynamicChildren,R,T,g,_,js(h,E),w):P||Y(d,h,T,null,g,_,js(h,E),w,!1),A>0){if(A&16)Ee(T,N,j,g,E);else if(A&2&&N.class!==j.class&&o(T,"class",null,j.class,E),A&4&&o(T,"style",N.style,j.style,E),A&8){const G=h.dynamicProps;for(let ie=0;ie<G.length;ie++){const te=G[ie],je=N[te],Ie=j[te];(Ie!==je||te==="value")&&o(T,te,je,Ie,E,g)}}A&1&&d.children!==h.children&&c(T,h.children)}else!P&&R==null&&Ee(T,N,j,g,E);(($=j.onVnodeUpdated)||U)&&Ue(()=>{$&&et($,g,h,d),U&&kt(h,d,g,"updated")},_)},de=(d,h,g,_,E,w,P)=>{for(let T=0;T<h.length;T++){const A=d[T],R=h[T],U=A.el&&(A.type===ge||!cn(A,R)||A.shapeFlag&198)?f(A.el):g;x(A,R,U,null,_,E,w,P,!0)}},Ee=(d,h,g,_,E)=>{if(h!==g){if(h!==oe)for(const w in h)!fn(w)&&!(w in g)&&o(d,w,h[w],null,E,_);for(const w in g){if(fn(w))continue;const P=g[w],T=h[w];P!==T&&w!=="value"&&o(d,w,T,P,E,_)}"value"in g&&o(d,"value",h.value,g.value,E)}},Fe=(d,h,g,_,E,w,P,T,A)=>{const R=h.el=d?d.el:l(""),U=h.anchor=d?d.anchor:l("");let{patchFlag:N,dynamicChildren:j,slotScopeIds:$}=h;$&&(T=T?T.concat($):$),d==null?(s(R,g,_),s(U,g,_),V(h.children||[],g,U,E,w,P,T,A)):N>0&&N&64&&j&&d.dynamicChildren?(de(d.dynamicChildren,j,g,E,w,P,T),(h.key!=null||E&&h===E.subTree)&&_l(d,h,!0)):Y(d,h,g,U,E,w,P,T,A)},Ce=(d,h,g,_,E,w,P,T,A)=>{h.slotScopeIds=T,d==null?h.shapeFlag&512?E.ctx.activate(h,g,_,P,A):It(h,g,_,E,w,P,A):gt(d,h,A)},It=(d,h,g,_,E,w,P)=>{const T=d.component=yf(d,_,E);if(il(d)&&(T.ctx.renderer=F),bf(T,!1,P),T.asyncDep){if(E&&E.registerDep(T,fe,P),!d.el){const A=T.subTree=W(Ct);C(null,A,h,g)}}else fe(T,d,h,g,E,w,P)},gt=(d,h,g)=>{const _=h.component=d.component;if(lf(d,h,g))if(_.asyncDep&&!_.asyncResolved){K(_,h,g);return}else _.next=h,_.update();else h.el=d.el,_.vnode=h},fe=(d,h,g,_,E,w,P)=>{const T=()=>{if(d.isMounted){let{next:N,bu:j,u:$,parent:G,vnode:ie}=d;{const Qe=vl(d);if(Qe){N&&(N.el=ie.el,K(d,N,P)),Qe.asyncDep.then(()=>{d.isUnmounted||T()});return}}let te=N,je;Nt(d,!1),N?(N.el=ie.el,K(d,N,P)):N=ie,j&&qn(j),(je=N.props&&N.props.onVnodeBeforeUpdate)&&et(je,G,N,ie),Nt(d,!0);const Ie=mo(d),Xe=d.subTree;d.subTree=Ie,x(Xe,Ie,f(Xe.el),v(Xe),d,E,w),N.el=Ie.el,te===null&&cf(d,Ie.el),$&&Ue($,E),(je=N.props&&N.props.onVnodeUpdated)&&Ue(()=>et(je,G,N,ie),E)}else{let N;const{el:j,props:$}=h,{bm:G,m:ie,parent:te,root:je,type:Ie}=d,Xe=pn(h);Nt(d,!1),G&&qn(G),!Xe&&(N=$&&$.onVnodeBeforeMount)&&et(N,te,h),Nt(d,!0);{je.ce&&je.ce._injectChildStyle(Ie);const Qe=d.subTree=mo(d);x(null,Qe,g,_,d,E,w),h.el=Qe.el}if(ie&&Ue(ie,E),!Xe&&(N=$&&$.onVnodeMounted)){const Qe=h;Ue(()=>et(N,te,Qe),E)}(h.shapeFlag&256||te&&pn(te.vnode)&&te.vnode.shapeFlag&256)&&d.a&&Ue(d.a,E),d.isMounted=!0,h=g=_=null}};d.scope.on();const A=d.effect=new Fi(T);d.scope.off();const R=d.update=A.run.bind(A),U=d.job=A.runIfDirty.bind(A);U.i=d,U.id=d.uid,A.scheduler=()=>Tr(U),Nt(d,!0),R()},K=(d,h,g)=>{h.component=d;const _=d.vnode.props;d.vnode=h,d.next=null,Vu(d,h.props,_,g),Ju(d,h.children,g),ht(),io(d),pt()},Y=(d,h,g,_,E,w,P,T,A=!1)=>{const R=d&&d.children,U=d?d.shapeFlag:0,N=h.children,{patchFlag:j,shapeFlag:$}=h;if(j>0){if(j&128){yt(R,N,g,_,E,w,P,T,A);return}else if(j&256){it(R,N,g,_,E,w,P,T,A);return}}$&8?(U&16&&$e(R,E,w),N!==R&&c(g,N)):U&16?$&16?yt(R,N,g,_,E,w,P,T,A):$e(R,E,w,!0):(U&8&&c(g,""),$&16&&V(N,g,_,E,w,P,T,A))},it=(d,h,g,_,E,w,P,T,A)=>{d=d||Jt,h=h||Jt;const R=d.length,U=h.length,N=Math.min(R,U);let j;for(j=0;j<N;j++){const $=h[j]=A?Et(h[j]):nt(h[j]);x(d[j],$,g,null,E,w,P,T,A)}R>U?$e(d,E,w,!0,!1,N):V(h,g,_,E,w,P,T,A,N)},yt=(d,h,g,_,E,w,P,T,A)=>{let R=0;const U=h.length;let N=d.length-1,j=U-1;for(;R<=N&&R<=j;){const $=d[R],G=h[R]=A?Et(h[R]):nt(h[R]);if(cn($,G))x($,G,g,null,E,w,P,T,A);else break;R++}for(;R<=N&&R<=j;){const $=d[N],G=h[j]=A?Et(h[j]):nt(h[j]);if(cn($,G))x($,G,g,null,E,w,P,T,A);else break;N--,j--}if(R>N){if(R<=j){const $=j+1,G=$<U?h[$].el:_;for(;R<=j;)x(null,h[R]=A?Et(h[R]):nt(h[R]),g,G,E,w,P,T,A),R++}}else if(R>j)for(;R<=N;)Pe(d[R],E,w,!0),R++;else{const $=R,G=R,ie=new Map;for(R=G;R<=j;R++){const De=h[R]=A?Et(h[R]):nt(h[R]);De.key!=null&&ie.set(De.key,R)}let te,je=0;const Ie=j-G+1;let Xe=!1,Qe=0;const rn=new Array(Ie);for(R=0;R<Ie;R++)rn[R]=0;for(R=$;R<=N;R++){const De=d[R];if(je>=Ie){Pe(De,E,w,!0);continue}let Ye;if(De.key!=null)Ye=ie.get(De.key);else for(te=G;te<=j;te++)if(rn[te-G]===0&&cn(De,h[te])){Ye=te;break}Ye===void 0?Pe(De,E,w,!0):(rn[Ye-G]=R+1,Ye>=Qe?Qe=Ye:Xe=!0,x(De,h[Ye],g,null,E,w,P,T,A),je++)}const Ur=Xe?Yu(rn):Jt;for(te=Ur.length-1,R=Ie-1;R>=0;R--){const De=G+R,Ye=h[De],Br=De+1<U?h[De+1].el:_;rn[R]===0?x(null,Ye,g,Br,E,w,P,T,A):Xe&&(te<0||R!==Ur[te]?Ge(Ye,g,Br,2):te--)}}},Ge=(d,h,g,_,E=null)=>{const{el:w,type:P,transition:T,children:A,shapeFlag:R}=d;if(R&6){Ge(d.component.subTree,h,g,_);return}if(R&128){d.suspense.move(h,g,_);return}if(R&64){P.move(d,h,g,F);return}if(P===ge){s(w,h,g);for(let N=0;N<A.length;N++)Ge(A[N],h,g,_);s(d.anchor,h,g);return}if(P===Kn){I(d,h,g);return}if(_!==2&&R&1&&T)if(_===0)T.beforeEnter(w),s(w,h,g),Ue(()=>T.enter(w),E);else{const{leave:N,delayLeave:j,afterLeave:$}=T,G=()=>{d.ctx.isUnmounted?r(w):s(w,h,g)},ie=()=>{N(w,()=>{G(),$&&$()})};j?j(w,G,ie):ie()}else s(w,h,g)},Pe=(d,h,g,_=!1,E=!1)=>{const{type:w,props:P,ref:T,children:A,dynamicChildren:R,shapeFlag:U,patchFlag:N,dirs:j,cacheIndex:$}=d;if(N===-2&&(E=!1),T!=null&&(ht(),ns(T,null,g,d,!0),pt()),$!=null&&(h.renderCache[$]=void 0),U&256){h.ctx.deactivate(d);return}const G=U&1&&j,ie=!pn(d);let te;if(ie&&(te=P&&P.onVnodeBeforeUnmount)&&et(te,h,d),U&6)Mn(d.component,g,_);else{if(U&128){d.suspense.unmount(g,_);return}G&&kt(d,null,h,"beforeUnmount"),U&64?d.type.remove(d,h,g,F,_):R&&!R.hasOnce&&(w!==ge||N>0&&N&64)?$e(R,h,g,!1,!0):(w===ge&&N&384||!E&&U&16)&&$e(A,h,g),_&&Ht(d)}(ie&&(te=P&&P.onVnodeUnmounted)||G)&&Ue(()=>{te&&et(te,h,d),G&&kt(d,null,h,"unmounted")},g)},Ht=d=>{const{type:h,el:g,anchor:_,transition:E}=d;if(h===ge){qt(g,_);return}if(h===Kn){L(d);return}const w=()=>{r(g),E&&!E.persisted&&E.afterLeave&&E.afterLeave()};if(d.shapeFlag&1&&E&&!E.persisted){const{leave:P,delayLeave:T}=E,A=()=>P(g,w);T?T(d.el,w,A):A()}else w()},qt=(d,h)=>{let g;for(;d!==h;)g=p(d),r(d),d=g;r(h)},Mn=(d,h,g)=>{const{bum:_,scope:E,job:w,subTree:P,um:T,m:A,a:R,parent:U,slots:{__:N}}=d;po(A),po(R),_&&qn(_),U&&B(N)&&N.forEach(j=>{U.renderCache[j]=void 0}),E.stop(),w&&(w.flags|=8,Pe(P,d,h,g)),T&&Ue(T,h),Ue(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},$e=(d,h,g,_=!1,E=!1,w=0)=>{for(let P=w;P<d.length;P++)Pe(d[P],h,g,_,E)},v=d=>{if(d.shapeFlag&6)return v(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),g=h&&h[yu];return g?p(g):h};let M=!1;const k=(d,h,g)=>{d==null?h._vnode&&Pe(h._vnode,null,null,!0):x(h._vnode||null,d,h,null,null,null,g),h._vnode=d,M||(M=!0,io(),nl(),M=!1)},F={p:x,um:Pe,m:Ge,r:Ht,mt:It,mc:V,pc:Y,pbc:de,n:v,o:e};return{render:k,hydrate:void 0,createApp:$u(k)}}function js({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Nt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Qu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function _l(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=Et(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&_l(i,l)),l.type===Es&&(l.el=i.el),l.type===Ct&&!l.el&&(l.el=i.el)}}function Yu(e){const t=e.slice(),n=[0];let s,r,o,i,l;const a=e.length;for(s=0;s<a;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function vl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:vl(t)}function po(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Zu=Symbol.for("v-scx"),ef=()=>qe(Zu);function gn(e,t,n){return wl(e,t,n)}function wl(e,t,n=oe){const{immediate:s,deep:r,flush:o,once:i}=n,l=xe({},n),a=t&&s||!t&&o!=="post";let u;if(An){if(o==="sync"){const m=ef();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=st,m.resume=st,m.pause=st,m}}const c=be;l.call=(m,b,x)=>ot(m,c,b,x);let f=!1;o==="post"?l.scheduler=m=>{Ue(m,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(m,b)=>{b?m():Tr(m)}),l.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const p=hu(e,t,l);return An&&(u?u.push(p):a&&p()),p}function tf(e,t,n){const s=this.proxy,r=me(e)?e.includes(".")?xl(s,e):()=>s[e]:e.bind(s,s);let o;H(t)?o=t:(o=t.handler,n=t);const i=Ln(this),l=wl(r,o.bind(s),n);return i(),l}function xl(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const nf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${$t(t)}Modifiers`];function sf(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||oe;let r=n;const o=t.startsWith("update:"),i=o&&nf(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>me(c)?c.trim():c)),i.number&&(r=n.map(Xn)));let l,a=s[l=Is(t)]||s[l=Is(Ke(t))];!a&&o&&(a=s[l=Is($t(t))]),a&&ot(a,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ot(u,e,6,r)}}function El(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!H(e)){const a=u=>{const c=El(u,t,!0);c&&(l=!0,xe(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ae(e)&&s.set(e,null),null):(B(o)?o.forEach(a=>i[a]=null):xe(i,o),ae(e)&&s.set(e,i),i)}function xs(e,t){return!e||!hs(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,$t(t))||ee(e,t))}function mo(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:p,setupState:m,ctx:b,inheritAttrs:x}=e,S=ts(e);let C,O;try{if(n.shapeFlag&4){const L=r||s,D=L;C=nt(u.call(D,L,c,f,m,p,b)),O=l}else{const L=t;C=nt(L.length>1?L(f,{attrs:l,slots:i,emit:a}):L(f,null)),O=t.props?l:rf(l)}}catch(L){yn.length=0,bs(L,e,1),C=W(Ct)}let I=C;if(O&&x!==!1){const L=Object.keys(O),{shapeFlag:D}=I;L.length&&D&7&&(o&&L.some(pr)&&(O=of(O,o)),I=Qt(I,O,!1,!0))}return n.dirs&&(I=Qt(I,null,!1,!0),I.dirs=I.dirs?I.dirs.concat(n.dirs):n.dirs),n.transition&&Cr(I,n.transition),C=I,ts(S),C}const rf=e=>{let t;for(const n in e)(n==="class"||n==="style"||hs(n))&&((t||(t={}))[n]=e[n]);return t},of=(e,t)=>{const n={};for(const s in e)(!pr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function lf(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?go(s,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const p=c[f];if(i[p]!==s[p]&&!xs(u,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?go(s,i,u):!0:!!i;return!1}function go(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!xs(n,o))return!0}return!1}function cf({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Sl=e=>e.__isSuspense;function af(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):gu(e)}const ge=Symbol.for("v-fgt"),Es=Symbol.for("v-txt"),Ct=Symbol.for("v-cmt"),Kn=Symbol.for("v-stc"),yn=[];let Be=null;function Ae(e=!1){yn.push(Be=e?null:[])}function uf(){yn.pop(),Be=yn[yn.length-1]||null}let Rn=1;function yo(e,t=!1){Rn+=e,e<0&&Be&&t&&(Be.hasOnce=!0)}function Rl(e){return e.dynamicChildren=Rn>0?Be||Jt:null,uf(),Rn>0&&Be&&Be.push(e),e}function ke(e,t,n,s,r,o){return Rl(X(e,t,n,s,r,o,!0))}function ff(e,t,n,s,r){return Rl(W(e,t,n,s,r,!0))}function rs(e){return e?e.__v_isVNode===!0:!1}function cn(e,t){return e.type===t.type&&e.key===t.key}const Al=({key:e})=>e??null,Wn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?me(e)||pe(e)||H(e)?{i:Ne,r:e,k:t,f:!!n}:e:null);function X(e,t=null,n=null,s=0,r=null,o=e===ge?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Al(t),ref:t&&Wn(t),scopeId:rl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ne};return l?(Lr(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=me(n)?8:16),Rn>0&&!i&&Be&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Be.push(a),a}const W=df;function df(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===ku)&&(e=Ct),rs(e)){const l=Qt(e,t,!0);return n&&Lr(l,n),Rn>0&&!o&&Be&&(l.shapeFlag&6?Be[Be.indexOf(e)]=l:Be.push(l)),l.patchFlag=-2,l}if(Ef(e)&&(e=e.__vccOpts),t){t=hf(t);let{class:l,style:a}=t;l&&!me(l)&&(t.class=br(l)),ae(a)&&(Ar(a)&&!B(a)&&(a=xe({},a)),t.style=yr(a))}const i=me(e)?1:Sl(e)?128:bu(e)?64:ae(e)?4:H(e)?2:0;return X(e,t,n,s,r,i,o,!0)}function hf(e){return e?Ar(e)||hl(e)?xe({},e):e:null}function Qt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?pf(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Al(u),ref:t&&t.ref?n&&o?B(o)?o.concat(Wn(t)):[o,Wn(t)]:Wn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ge?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qt(e.ssContent),ssFallback:e.ssFallback&&Qt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&Cr(c,a.clone(c)),c}function ue(e=" ",t=0){return W(Es,null,e,t)}function np(e,t){const n=W(Kn,null,e);return n.staticCount=t,n}function bt(e="",t=!1){return t?(Ae(),ff(Ct,null,e)):W(Ct,null,e)}function nt(e){return e==null||typeof e=="boolean"?W(Ct):B(e)?W(ge,null,e.slice()):rs(e)?Et(e):W(Es,null,String(e))}function Et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qt(e)}function Lr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Lr(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!hl(t)?t._ctx=Ne:r===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else H(t)?(t={default:t,_ctx:Ne},n=32):(t=String(t),s&64?(n=16,t=[ue(t)]):n=8);e.children=t,e.shapeFlag|=n}function pf(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=br([t.class,s.class]));else if(r==="style")t.style=yr([t.style,s.style]);else if(hs(r)){const o=t[r],i=s[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function et(e,t,n,s=null){ot(e,t,7,[n,s])}const mf=ul();let gf=0;function yf(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||mf,o={uid:gf++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ni(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ml(s,r),emitsOptions:El(s,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:s.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=sf.bind(null,o),e.ce&&e.ce(o),o}let be=null,os,sr;{const e=gs(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};os=t("__VUE_INSTANCE_SETTERS__",n=>be=n),sr=t("__VUE_SSR_SETTERS__",n=>An=n)}const Ln=e=>{const t=be;return os(e),e.scope.on(),()=>{e.scope.off(),os(t)}},bo=()=>{be&&be.scope.off(),os(null)};function Ol(e){return e.vnode.shapeFlag&4}let An=!1;function bf(e,t=!1,n=!1){t&&sr(t);const{props:s,children:r}=e.vnode,o=Ol(e);qu(e,s,o,t),zu(e,r,n||t);const i=o?_f(e,t):void 0;return t&&sr(!1),i}function _f(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Lu);const{setup:s}=n;if(s){ht();const r=e.setupContext=s.length>1?wf(e):null,o=Ln(e),i=Nn(s,e,0,[e.props,r]),l=Ai(i);if(pt(),o(),(l||e.sp)&&!pn(e)&&ol(e),l){if(i.then(bo,bo),t)return i.then(a=>{_o(e,a)}).catch(a=>{bs(a,e,0)});e.asyncDep=i}else _o(e,i)}else Tl(e)}function _o(e,t,n){H(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ae(t)&&(e.setupState=Zi(t)),Tl(e)}function Tl(e,t,n){const s=e.type;e.render||(e.render=s.render||st);{const r=Ln(e);ht();try{Mu(e)}finally{pt(),r()}}}const vf={get(e,t){return ve(e,"get",""),e[t]}};function wf(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,vf),slots:e.slots,emit:e.emit,expose:t}}function Ss(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Zi(Or(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in mn)return mn[n](e)},has(t,n){return n in t||n in mn}})):e.proxy}function xf(e,t=!0){return H(e)?e.displayName||e.name:e.name||t&&e.__name}function Ef(e){return H(e)&&"__vccOpts"in e}const Te=(e,t)=>fu(e,t,An);function Cl(e,t,n){const s=arguments.length;return s===2?ae(t)&&!B(t)?rs(t)?W(e,null,[t]):W(e,t):W(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&rs(n)&&(n=[n]),W(e,t,n))}const Sf="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let rr;const vo=typeof window<"u"&&window.trustedTypes;if(vo)try{rr=vo.createPolicy("vue",{createHTML:e=>e})}catch{}const Pl=rr?e=>rr.createHTML(e):e=>e,Rf="http://www.w3.org/2000/svg",Af="http://www.w3.org/1998/Math/MathML",at=typeof document<"u"?document:null,wo=at&&at.createElement("template"),Of={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?at.createElementNS(Rf,e):t==="mathml"?at.createElementNS(Af,e):n?at.createElement(e,{is:n}):at.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>at.createTextNode(e),createComment:e=>at.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>at.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{wo.innerHTML=Pl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=wo.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Tf=Symbol("_vtc");function Cf(e,t,n){const s=e[Tf];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const xo=Symbol("_vod"),Pf=Symbol("_vsh"),If=Symbol(""),kf=/(^|;)\s*display\s*:/;function Nf(e,t,n){const s=e.style,r=me(n);let o=!1;if(n&&!r){if(t)if(me(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&zn(s,l,"")}else for(const i in t)n[i]==null&&zn(s,i,"");for(const i in n)i==="display"&&(o=!0),zn(s,i,n[i])}else if(r){if(t!==n){const i=s[If];i&&(n+=";"+i),s.cssText=n,o=kf.test(n)}}else t&&e.removeAttribute("style");xo in e&&(e[xo]=o?s.display:"",e[Pf]&&(s.display="none"))}const Eo=/\s*!important$/;function zn(e,t,n){if(B(n))n.forEach(s=>zn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Lf(e,t);Eo.test(n)?e.setProperty($t(s),n.replace(Eo,""),"important"):e[s]=n}}const So=["Webkit","Moz","ms"],Ds={};function Lf(e,t){const n=Ds[t];if(n)return n;let s=Ke(t);if(s!=="filter"&&s in e)return Ds[t]=s;s=ms(s);for(let r=0;r<So.length;r++){const o=So[r]+s;if(o in e)return Ds[t]=o}return t}const Ro="http://www.w3.org/1999/xlink";function Ao(e,t,n,s,r,o=Fa(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ro,t.slice(6,t.length)):e.setAttributeNS(Ro,t,n):n==null||o&&!Pi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":rt(n)?String(n):n)}function Oo(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Pl(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Pi(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function dt(e,t,n,s){e.addEventListener(t,n,s)}function Mf(e,t,n,s){e.removeEventListener(t,n,s)}const To=Symbol("_vei");function Ff(e,t,n,s,r=null){const o=e[To]||(e[To]={}),i=o[t];if(s&&i)i.value=s;else{const[l,a]=jf(t);if(s){const u=o[t]=Bf(s,r);dt(e,l,u,a)}else i&&(Mf(e,l,i,a),o[t]=void 0)}}const Co=/(?:Once|Passive|Capture)$/;function jf(e){let t;if(Co.test(e)){t={};let s;for(;s=e.match(Co);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):$t(e.slice(2)),t]}let Us=0;const Df=Promise.resolve(),Uf=()=>Us||(Df.then(()=>Us=0),Us=Date.now());function Bf(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ot($f(s,n.value),t,5,[s])};return n.value=e,n.attached=Uf(),n}function $f(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Po=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Hf=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Cf(e,s,i):t==="style"?Nf(e,n,s):hs(t)?pr(t)||Ff(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):qf(e,t,s,i))?(Oo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ao(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!me(s))?Oo(e,Ke(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Ao(e,t,s,i))};function qf(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Po(t)&&H(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Po(t)&&me(n)?!1:t in e}const Pt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>qn(t,n):t};function Vf(e){e.target.composing=!0}function Io(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ve=Symbol("_assign"),sp={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Ve]=Pt(r);const o=s||r.props&&r.props.type==="number";dt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=Xn(l)),e[Ve](l)}),n&&dt(e,"change",()=>{e.value=e.value.trim()}),t||(dt(e,"compositionstart",Vf),dt(e,"compositionend",Io),dt(e,"change",Io))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Ve]=Pt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Xn(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===a)||(e.value=a))}},rp={deep:!0,created(e,t,n){e[Ve]=Pt(n),dt(e,"change",()=>{const s=e._modelValue,r=Yt(e),o=e.checked,i=e[Ve];if(B(s)){const l=_r(s,r),a=l!==-1;if(o&&!a)i(s.concat(r));else if(!o&&a){const u=[...s];u.splice(l,1),i(u)}}else if(sn(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(Il(e,o))})},mounted:ko,beforeUpdate(e,t,n){e[Ve]=Pt(n),ko(e,t,n)}};function ko(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(B(t))r=_r(t,s.props.value)>-1;else if(sn(t))r=t.has(s.props.value);else{if(t===n)return;r=Bt(t,Il(e,!0))}e.checked!==r&&(e.checked=r)}const op={created(e,{value:t},n){e.checked=Bt(t,n.props.value),e[Ve]=Pt(n),dt(e,"change",()=>{e[Ve](Yt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[Ve]=Pt(s),t!==n&&(e.checked=Bt(t,s.props.value))}},ip={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=sn(t);dt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Xn(Yt(i)):Yt(i));e[Ve](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,_s(()=>{e._assigning=!1})}),e[Ve]=Pt(s)},mounted(e,{value:t}){No(e,t)},beforeUpdate(e,t,n){e[Ve]=Pt(n)},updated(e,{value:t}){e._assigning||No(e,t)}};function No(e,t){const n=e.multiple,s=B(t);if(!(n&&!s&&!sn(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Yt(i);if(n)if(s){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(u=>String(u)===String(l)):i.selected=_r(t,l)>-1}else i.selected=t.has(l);else if(Bt(Yt(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Yt(e){return"_value"in e?e._value:e.value}function Il(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Kf=["ctrl","shift","alt","meta"],Wf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Kf.some(n=>e[`${n}Key`]&&!t.includes(n))},lp=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Wf[t[i]];if(l&&l(r,t))return}return e(r,...o)})},zf=xe({patchProp:Hf},Of);let Lo;function Jf(){return Lo||(Lo=Gu(zf))}const Gf=(...e)=>{const t=Jf().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Qf(s);if(!r)return;const o=t._component;!H(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Xf(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Xf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Qf(e){return me(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let kl;const Rs=e=>kl=e,Nl=Symbol();function or(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var bn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(bn||(bn={}));function Yf(){const e=Li(!0),t=e.run(()=>Ot({}));let n=[],s=[];const r=Or({install(o){Rs(r),r._a=o,o.provide(Nl,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Ll=()=>{};function Mo(e,t,n,s=Ll){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&Mi()&&Da(r),r}function Kt(e,...t){e.slice().forEach(n=>{n(...t)})}const Zf=e=>e(),Fo=Symbol(),Bs=Symbol();function ir(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];or(r)&&or(s)&&e.hasOwnProperty(n)&&!pe(s)&&!At(s)?e[n]=ir(r,s):e[n]=s}return e}const ed=Symbol();function td(e){return!or(e)||!Object.prototype.hasOwnProperty.call(e,ed)}const{assign:wt}=Object;function nd(e){return!!(pe(e)&&e.effect)}function sd(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let a;function u(){l||(n.state.value[e]=r?r():{});const c=lu(n.state.value[e]);return wt(c,o,Object.keys(i||{}).reduce((f,p)=>(f[p]=Or(Te(()=>{Rs(n);const m=n._s.get(e);return i[p].call(m,m)})),f),{}))}return a=Ml(e,u,t,n,s,!0),a}function Ml(e,t,n={},s,r,o){let i;const l=wt({actions:{}},n),a={deep:!0};let u,c,f=[],p=[],m;const b=s.state.value[e];!o&&!b&&(s.state.value[e]={}),Ot({});let x;function S(V){let z;u=c=!1,typeof V=="function"?(V(s.state.value[e]),z={type:bn.patchFunction,storeId:e,events:m}):(ir(s.state.value[e],V),z={type:bn.patchObject,payload:V,storeId:e,events:m});const de=x=Symbol();_s().then(()=>{x===de&&(u=!0)}),c=!0,Kt(f,z,s.state.value[e])}const C=o?function(){const{state:z}=n,de=z?z():{};this.$patch(Ee=>{wt(Ee,de)})}:Ll;function O(){i.stop(),f=[],p=[],s._s.delete(e)}const I=(V,z="")=>{if(Fo in V)return V[Bs]=z,V;const de=function(){Rs(s);const Ee=Array.from(arguments),Fe=[],Ce=[];function It(K){Fe.push(K)}function gt(K){Ce.push(K)}Kt(p,{args:Ee,name:de[Bs],store:D,after:It,onError:gt});let fe;try{fe=V.apply(this&&this.$id===e?this:D,Ee)}catch(K){throw Kt(Ce,K),K}return fe instanceof Promise?fe.then(K=>(Kt(Fe,K),K)).catch(K=>(Kt(Ce,K),Promise.reject(K))):(Kt(Fe,fe),fe)};return de[Fo]=!0,de[Bs]=z,de},L={_p:s,$id:e,$onAction:Mo.bind(null,p),$patch:S,$reset:C,$subscribe(V,z={}){const de=Mo(f,V,z.detached,()=>Ee()),Ee=i.run(()=>gn(()=>s.state.value[e],Fe=>{(z.flush==="sync"?c:u)&&V({storeId:e,type:bn.direct,events:m},Fe)},wt({},a,z)));return de},$dispose:O},D=kn(L);s._s.set(e,D);const J=(s._a&&s._a.runWithContext||Zf)(()=>s._e.run(()=>(i=Li()).run(()=>t({action:I}))));for(const V in J){const z=J[V];if(pe(z)&&!nd(z)||At(z))o||(b&&td(z)&&(pe(z)?z.value=b[V]:ir(z,b[V])),s.state.value[e][V]=z);else if(typeof z=="function"){const de=I(z,V);J[V]=de,l.actions[V]=z}}return wt(D,J),wt(Q(D),J),Object.defineProperty(D,"$state",{get:()=>s.state.value[e],set:V=>{S(z=>{wt(z,V)})}}),s._p.forEach(V=>{wt(D,i.run(()=>V({store:D,app:s._a,pinia:s,options:l})))}),b&&o&&n.hydrate&&n.hydrate(D.$state,b),u=!0,c=!0,D}/*! #__NO_SIDE_EFFECTS__ */function rd(e,t,n){let s;const r=typeof t=="function";s=r?n:t;function o(i,l){const a=Hu();return i=i||(a?qe(Nl,null):null),i&&Rs(i),i=kl,i._s.has(e)||(r?Ml(e,t,s,i):sd(e,s,i)),i._s.get(e)}return o.$id=e,o}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const zt=typeof document<"u";function Fl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function od(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Fl(e.default)}const Z=Object.assign;function $s(e,t){const n={};for(const s in t){const r=t[s];n[s]=ze(r)?r.map(e):e(r)}return n}const _n=()=>{},ze=Array.isArray,jl=/#/g,id=/&/g,ld=/\//g,cd=/=/g,ad=/\?/g,Dl=/\+/g,ud=/%5B/g,fd=/%5D/g,Ul=/%5E/g,dd=/%60/g,Bl=/%7B/g,hd=/%7C/g,$l=/%7D/g,pd=/%20/g;function Mr(e){return encodeURI(""+e).replace(hd,"|").replace(ud,"[").replace(fd,"]")}function md(e){return Mr(e).replace(Bl,"{").replace($l,"}").replace(Ul,"^")}function lr(e){return Mr(e).replace(Dl,"%2B").replace(pd,"+").replace(jl,"%23").replace(id,"%26").replace(dd,"`").replace(Bl,"{").replace($l,"}").replace(Ul,"^")}function gd(e){return lr(e).replace(cd,"%3D")}function yd(e){return Mr(e).replace(jl,"%23").replace(ad,"%3F")}function bd(e){return e==null?"":yd(e).replace(ld,"%2F")}function On(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const _d=/\/$/,vd=e=>e.replace(_d,"");function Hs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Sd(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:On(i)}}function wd(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function jo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function xd(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Zt(t.matched[s],n.matched[r])&&Hl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Zt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Hl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ed(e[n],t[n]))return!1;return!0}function Ed(e,t){return ze(e)?Do(e,t):ze(t)?Do(t,e):e===t}function Do(e,t){return ze(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Sd(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const _t={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Tn;(function(e){e.pop="pop",e.push="push"})(Tn||(Tn={}));var vn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(vn||(vn={}));function Rd(e){if(!e)if(zt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),vd(e)}const Ad=/^[^#]+#/;function Od(e,t){return e.replace(Ad,"#")+t}function Td(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const As=()=>({left:window.scrollX,top:window.scrollY});function Cd(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Td(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Uo(e,t){return(history.state?history.state.position-t:-1)+e}const cr=new Map;function Pd(e,t){cr.set(e,t)}function Id(e){const t=cr.get(e);return cr.delete(e),t}let kd=()=>location.protocol+"//"+location.host;function ql(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),jo(a,"")}return jo(n,e)+s+r}function Nd(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=ql(e,location),b=n.value,x=t.value;let S=0;if(p){if(n.value=m,t.value=p,i&&i===b){i=null;return}S=x?p.position-x.position:0}else s(m);r.forEach(C=>{C(n.value,b,{delta:S,type:Tn.pop,direction:S?S>0?vn.forward:vn.back:vn.unknown})})};function a(){i=n.value}function u(p){r.push(p);const m=()=>{const b=r.indexOf(p);b>-1&&r.splice(b,1)};return o.push(m),m}function c(){const{history:p}=window;p.state&&p.replaceState(Z({},p.state,{scroll:As()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function Bo(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?As():null}}function Ld(e){const{history:t,location:n}=window,s={value:ql(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:kd()+e+a;try{t[c?"replaceState":"pushState"](u,"",p),r.value=u}catch(m){console.error(m),n[c?"replace":"assign"](p)}}function i(a,u){const c=Z({},t.state,Bo(r.value.back,a,r.value.forward,!0),u,{position:r.value.position});o(a,c,!0),s.value=a}function l(a,u){const c=Z({},r.value,t.state,{forward:a,scroll:As()});o(c.current,c,!0);const f=Z({},Bo(s.value,a,null),{position:c.position+1},u);o(a,f,!1),s.value=a}return{location:s,state:r,push:l,replace:i}}function Md(e){e=Rd(e);const t=Ld(e),n=Nd(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Z({location:"",base:e,go:s,createHref:Od.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Fd(e){return typeof e=="string"||e&&typeof e=="object"}function Vl(e){return typeof e=="string"||typeof e=="symbol"}const Kl=Symbol("");var $o;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($o||($o={}));function en(e,t){return Z(new Error,{type:e,[Kl]:!0},t)}function ct(e,t){return e instanceof Error&&Kl in e&&(t==null||!!(e.type&t))}const Ho="[^/]+?",jd={sensitive:!1,strict:!1,start:!0,end:!0},Dd=/[.+*?^${}()[\]/\\]/g;function Ud(e,t){const n=Z({},jd,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const p=u[f];let m=40+(n.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(Dd,"\\$&"),m+=40;else if(p.type===1){const{value:b,repeatable:x,optional:S,regexp:C}=p;o.push({name:b,repeatable:x,optional:S});const O=C||Ho;if(O!==Ho){m+=10;try{new RegExp(`(${O})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${b}" (${O}): `+L.message)}}let I=x?`((?:${O})(?:/(?:${O}))*)`:`(${O})`;f||(I=S&&u.length<2?`(?:/${I})`:"/"+I),S&&(I+="?"),r+=I,m+=20,S&&(m+=-8),x&&(m+=-20),O===".*"&&(m+=-50)}c.push(m)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let p=1;p<c.length;p++){const m=c[p]||"",b=o[p-1];f[b.name]=m&&b.repeatable?m.split("/"):m}return f}function a(u){let c="",f=!1;for(const p of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const m of p)if(m.type===0)c+=m.value;else if(m.type===1){const{value:b,repeatable:x,optional:S}=m,C=b in u?u[b]:"";if(ze(C)&&!x)throw new Error(`Provided param "${b}" is an array but it is not repeatable (* or + modifiers)`);const O=ze(C)?C.join("/"):C;if(!O)if(S)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${b}"`);c+=O}}return c||"/"}return{re:i,score:s,keys:o,parse:l,stringify:a}}function Bd(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Wl(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Bd(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(qo(s))return 1;if(qo(r))return-1}return r.length-s.length}function qo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const $d={type:0,value:""},Hd=/[a-zA-Z0-9_]/;function qd(e){if(!e)return[[]];if(e==="/")return[[$d]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:a==="("?n=2:Hd.test(a)?p():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function Vd(e,t,n){const s=Ud(qd(e.path),n),r=Z(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Kd(e,t){const n=[],s=new Map;t=zo({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,p,m){const b=!m,x=Ko(f);x.aliasOf=m&&m.record;const S=zo(t,f),C=[x];if("alias"in f){const L=typeof f.alias=="string"?[f.alias]:f.alias;for(const D of L)C.push(Ko(Z({},x,{components:m?m.record.components:x.components,path:D,aliasOf:m?m.record:x})))}let O,I;for(const L of C){const{path:D}=L;if(p&&D[0]!=="/"){const se=p.record.path,J=se[se.length-1]==="/"?"":"/";L.path=p.record.path+(D&&J+D)}if(O=Vd(L,p,S),m?m.alias.push(O):(I=I||O,I!==O&&I.alias.push(O),b&&f.name&&!Wo(O)&&i(f.name)),zl(O)&&a(O),x.children){const se=x.children;for(let J=0;J<se.length;J++)o(se[J],O,m&&m.children[J])}m=m||O}return I?()=>{i(I)}:_n}function i(f){if(Vl(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const p=Jd(f,n);n.splice(p,0,f),f.record.name&&!Wo(f)&&s.set(f.record.name,f)}function u(f,p){let m,b={},x,S;if("name"in f&&f.name){if(m=s.get(f.name),!m)throw en(1,{location:f});S=m.record.name,b=Z(Vo(p.params,m.keys.filter(I=>!I.optional).concat(m.parent?m.parent.keys.filter(I=>I.optional):[]).map(I=>I.name)),f.params&&Vo(f.params,m.keys.map(I=>I.name))),x=m.stringify(b)}else if(f.path!=null)x=f.path,m=n.find(I=>I.re.test(x)),m&&(b=m.parse(x),S=m.record.name);else{if(m=p.name?s.get(p.name):n.find(I=>I.re.test(p.path)),!m)throw en(1,{location:f,currentLocation:p});S=m.record.name,b=Z({},p.params,f.params),x=m.stringify(b)}const C=[];let O=m;for(;O;)C.unshift(O.record),O=O.parent;return{name:S,path:x,params:b,matched:C,meta:zd(C)}}e.forEach(f=>o(f));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function Vo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Ko(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Wd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Wd(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Wo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function zd(e){return e.reduce((t,n)=>Z(t,n.meta),{})}function zo(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Jd(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Wl(e,t[o])<0?s=o:n=o+1}const r=Gd(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Gd(e){let t=e;for(;t=t.parent;)if(zl(t)&&Wl(e,t)===0)return t}function zl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Xd(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Dl," "),i=o.indexOf("="),l=On(i<0?o:o.slice(0,i)),a=i<0?null:On(o.slice(i+1));if(l in t){let u=t[l];ze(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function Jo(e){let t="";for(let n in e){const s=e[n];if(n=gd(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ze(s)?s.map(o=>o&&lr(o)):[s&&lr(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Qd(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ze(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Yd=Symbol(""),Go=Symbol(""),Os=Symbol(""),Fr=Symbol(""),ar=Symbol("");function an(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function St(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const u=p=>{p===!1?a(en(4,{from:n,to:t})):p instanceof Error?a(p):Fd(p)?a(en(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},c=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(p=>a(p))})}function qs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Fl(a)){const c=(a.__vccOpts||a)[t];c&&o.push(St(c,n,s,i,l,r))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=od(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const m=(f.__vccOpts||f)[t];return m&&St(m,n,s,i,l,r)()}))}}return o}function Xo(e){const t=qe(Os),n=qe(Fr),s=Te(()=>{const a=he(e.to);return t.resolve(a)}),r=Te(()=>{const{matched:a}=s.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const p=f.findIndex(Zt.bind(null,c));if(p>-1)return p;const m=Qo(a[u-2]);return u>1&&Qo(c)===m&&f[f.length-1].path!==m?f.findIndex(Zt.bind(null,a[u-2])):p}),o=Te(()=>r.value>-1&&sh(n.params,s.value.params)),i=Te(()=>r.value>-1&&r.value===n.matched.length-1&&Hl(n.params,s.value.params));function l(a={}){if(nh(a)){const u=t[he(e.replace)?"replace":"push"](he(e.to)).catch(_n);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:Te(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function Zd(e){return e.length===1?e[0]:e}const eh=vs({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Xo,setup(e,{slots:t}){const n=kn(Xo(e)),{options:s}=qe(Os),r=Te(()=>({[Yo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Yo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Zd(t.default(n));return e.custom?o:Cl("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),th=eh;function nh(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function sh(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!ze(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Qo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Yo=(e,t,n)=>e??t??n,rh=vs({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=qe(ar),r=Te(()=>e.route||s.value),o=qe(Go,0),i=Te(()=>{let u=he(o);const{matched:c}=r.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=Te(()=>r.value.matched[i.value]);Vn(Go,Te(()=>i.value+1)),Vn(Yd,l),Vn(ar,r);const a=Ot();return gn(()=>[a.value,l.value,e.name],([u,c,f],[p,m,b])=>{c&&(c.instances[f]=u,m&&m!==c&&u&&u===p&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),u&&c&&(!m||!Zt(c,m)||!p)&&(c.enterCallbacks[f]||[]).forEach(x=>x(u))},{flush:"post"}),()=>{const u=r.value,c=e.name,f=l.value,p=f&&f.components[c];if(!p)return Zo(n.default,{Component:p,route:u});const m=f.props[c],b=m?m===!0?u.params:typeof m=="function"?m(u):m:null,S=Cl(p,Z({},b,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return Zo(n.default,{Component:S,route:u})||S}}});function Zo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Jl=rh;function oh(e){const t=Kd(e.routes,e),n=e.parseQuery||Xd,s=e.stringifyQuery||Jo,r=e.history,o=an(),i=an(),l=an(),a=ru(_t);let u=_t;zt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=$s.bind(null,v=>""+v),f=$s.bind(null,bd),p=$s.bind(null,On);function m(v,M){let k,F;return Vl(v)?(k=t.getRecordMatcher(v),F=M):F=v,t.addRoute(F,k)}function b(v){const M=t.getRecordMatcher(v);M&&t.removeRoute(M)}function x(){return t.getRoutes().map(v=>v.record)}function S(v){return!!t.getRecordMatcher(v)}function C(v,M){if(M=Z({},M||a.value),typeof v=="string"){const g=Hs(n,v,M.path),_=t.resolve({path:g.path},M),E=r.createHref(g.fullPath);return Z(g,_,{params:p(_.params),hash:On(g.hash),redirectedFrom:void 0,href:E})}let k;if(v.path!=null)k=Z({},v,{path:Hs(n,v.path,M.path).path});else{const g=Z({},v.params);for(const _ in g)g[_]==null&&delete g[_];k=Z({},v,{params:f(g)}),M.params=f(M.params)}const F=t.resolve(k,M),re=v.hash||"";F.params=c(p(F.params));const d=wd(s,Z({},v,{hash:md(re),path:F.path})),h=r.createHref(d);return Z({fullPath:d,hash:re,query:s===Jo?Qd(v.query):v.query||{}},F,{redirectedFrom:void 0,href:h})}function O(v){return typeof v=="string"?Hs(n,v,a.value.path):Z({},v)}function I(v,M){if(u!==v)return en(8,{from:M,to:v})}function L(v){return J(v)}function D(v){return L(Z(O(v),{replace:!0}))}function se(v){const M=v.matched[v.matched.length-1];if(M&&M.redirect){const{redirect:k}=M;let F=typeof k=="function"?k(v):k;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=O(F):{path:F},F.params={}),Z({query:v.query,hash:v.hash,params:F.path!=null?{}:v.params},F)}}function J(v,M){const k=u=C(v),F=a.value,re=v.state,d=v.force,h=v.replace===!0,g=se(k);if(g)return J(Z(O(g),{state:typeof g=="object"?Z({},re,g.state):re,force:d,replace:h}),M||k);const _=k;_.redirectedFrom=M;let E;return!d&&xd(s,F,k)&&(E=en(16,{to:_,from:F}),Ge(F,F,!0,!1)),(E?Promise.resolve(E):de(_,F)).catch(w=>ct(w)?ct(w,2)?w:yt(w):Y(w,_,F)).then(w=>{if(w){if(ct(w,2))return J(Z({replace:h},O(w.to),{state:typeof w.to=="object"?Z({},re,w.to.state):re,force:d}),M||_)}else w=Fe(_,F,!0,h,re);return Ee(_,F,w),w})}function V(v,M){const k=I(v,M);return k?Promise.reject(k):Promise.resolve()}function z(v){const M=qt.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(v):v()}function de(v,M){let k;const[F,re,d]=ih(v,M);k=qs(F.reverse(),"beforeRouteLeave",v,M);for(const g of F)g.leaveGuards.forEach(_=>{k.push(St(_,v,M))});const h=V.bind(null,v,M);return k.push(h),$e(k).then(()=>{k=[];for(const g of o.list())k.push(St(g,v,M));return k.push(h),$e(k)}).then(()=>{k=qs(re,"beforeRouteUpdate",v,M);for(const g of re)g.updateGuards.forEach(_=>{k.push(St(_,v,M))});return k.push(h),$e(k)}).then(()=>{k=[];for(const g of d)if(g.beforeEnter)if(ze(g.beforeEnter))for(const _ of g.beforeEnter)k.push(St(_,v,M));else k.push(St(g.beforeEnter,v,M));return k.push(h),$e(k)}).then(()=>(v.matched.forEach(g=>g.enterCallbacks={}),k=qs(d,"beforeRouteEnter",v,M,z),k.push(h),$e(k))).then(()=>{k=[];for(const g of i.list())k.push(St(g,v,M));return k.push(h),$e(k)}).catch(g=>ct(g,8)?g:Promise.reject(g))}function Ee(v,M,k){l.list().forEach(F=>z(()=>F(v,M,k)))}function Fe(v,M,k,F,re){const d=I(v,M);if(d)return d;const h=M===_t,g=zt?history.state:{};k&&(F||h?r.replace(v.fullPath,Z({scroll:h&&g&&g.scroll},re)):r.push(v.fullPath,re)),a.value=v,Ge(v,M,k,h),yt()}let Ce;function It(){Ce||(Ce=r.listen((v,M,k)=>{if(!Mn.listening)return;const F=C(v),re=se(F);if(re){J(Z(re,{replace:!0,force:!0}),F).catch(_n);return}u=F;const d=a.value;zt&&Pd(Uo(d.fullPath,k.delta),As()),de(F,d).catch(h=>ct(h,12)?h:ct(h,2)?(J(Z(O(h.to),{force:!0}),F).then(g=>{ct(g,20)&&!k.delta&&k.type===Tn.pop&&r.go(-1,!1)}).catch(_n),Promise.reject()):(k.delta&&r.go(-k.delta,!1),Y(h,F,d))).then(h=>{h=h||Fe(F,d,!1),h&&(k.delta&&!ct(h,8)?r.go(-k.delta,!1):k.type===Tn.pop&&ct(h,20)&&r.go(-1,!1)),Ee(F,d,h)}).catch(_n)}))}let gt=an(),fe=an(),K;function Y(v,M,k){yt(v);const F=fe.list();return F.length?F.forEach(re=>re(v,M,k)):console.error(v),Promise.reject(v)}function it(){return K&&a.value!==_t?Promise.resolve():new Promise((v,M)=>{gt.add([v,M])})}function yt(v){return K||(K=!v,It(),gt.list().forEach(([M,k])=>v?k(v):M()),gt.reset()),v}function Ge(v,M,k,F){const{scrollBehavior:re}=e;if(!zt||!re)return Promise.resolve();const d=!k&&Id(Uo(v.fullPath,0))||(F||!k)&&history.state&&history.state.scroll||null;return _s().then(()=>re(v,M,d)).then(h=>h&&Cd(h)).catch(h=>Y(h,v,M))}const Pe=v=>r.go(v);let Ht;const qt=new Set,Mn={currentRoute:a,listening:!0,addRoute:m,removeRoute:b,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:x,resolve:C,options:e,push:L,replace:D,go:Pe,back:()=>Pe(-1),forward:()=>Pe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:fe.add,isReady:it,install(v){const M=this;v.component("RouterLink",th),v.component("RouterView",Jl),v.config.globalProperties.$router=M,Object.defineProperty(v.config.globalProperties,"$route",{enumerable:!0,get:()=>he(a)}),zt&&!Ht&&a.value===_t&&(Ht=!0,L(r.location).catch(re=>{}));const k={};for(const re in _t)Object.defineProperty(k,re,{get:()=>a.value[re],enumerable:!0});v.provide(Os,M),v.provide(Fr,Xi(k)),v.provide(ar,a);const F=v.unmount;qt.add(v),v.unmount=function(){qt.delete(v),qt.size<1&&(u=_t,Ce&&Ce(),Ce=null,a.value=_t,Ht=!1,K=!1),F()}}};function $e(v){return v.reduce((M,k)=>M.then(()=>z(k)),Promise.resolve())}return Mn}function ih(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>Zt(u,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>Zt(u,a))||r.push(a))}return[n,s,r]}function lh(){return qe(Os)}function cp(e){return qe(Fr)}const jr=rd("auth",()=>{const e=Ot(null),t=Ot(localStorage.getItem("token")),n=Te(()=>!!t.value&&!!e.value),s=Te(()=>{var a;return((a=e.value)==null?void 0:a.role)||null});t.value&&(ne.defaults.headers.common.Authorization=`Bearer ${t.value}`);async function r(a,u){var c,f;try{const p=await ne.post("/api/auth/login",{email:a,password:u}),{user:m,token:b}=p.data;return e.value=m,t.value=b,localStorage.setItem("token",b),localStorage.setItem("user",JSON.stringify(m)),ne.defaults.headers.common.Authorization=`Bearer ${b}`,{success:!0,user:m}}catch(p){return console.error("Login error:",p),{success:!1,error:((f=(c=p.response)==null?void 0:c.data)==null?void 0:f.message)||"Login failed"}}}async function o(a){var u,c;try{const f=await ne.post("/api/auth/register",a),{user:p,token:m}=f.data;return e.value=p,t.value=m,localStorage.setItem("token",m),localStorage.setItem("user",JSON.stringify(p)),ne.defaults.headers.common.Authorization=`Bearer ${m}`,{success:!0,user:p}}catch(f){return console.error("Registration error:",f),{success:!1,error:((c=(u=f.response)==null?void 0:u.data)==null?void 0:c.message)||"Registration failed"}}}function i(){e.value=null,t.value=null,localStorage.removeItem("token"),localStorage.removeItem("user"),delete ne.defaults.headers.common.Authorization}function l(){const a=localStorage.getItem("token"),u=localStorage.getItem("user");if(a&&u)try{t.value=a,e.value=JSON.parse(u),ne.defaults.headers.common.Authorization=`Bearer ${a}`}catch(c){console.error("Error parsing stored user data:",c),i()}}return{user:e,token:t,isAuthenticated:n,userRole:s,login:r,register:o,logout:i,initializeAuth:l}}),ch={class:"bg-white shadow-sm border-b border-gray-200"},ah={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},uh={class:"flex justify-between items-center h-16"},fh={class:"flex items-center"},dh={class:"hidden md:flex items-center space-x-8"},hh={class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},ph={class:"text-sm font-medium"},mh={class:"hidden sm:block"},gh={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"},yh={class:"px-4 py-2 text-sm text-gray-700 border-b border-gray-200"},bh={class:"font-medium"},_h={class:"text-gray-500"},vh={class:"text-xs text-primary-600 capitalize"},wh={class:"md:hidden"},xh={key:0,class:"md:hidden border-t border-gray-200 py-4"},Eh={class:"space-y-2"},Sh={class:"border-t border-gray-200 pt-2 mt-2"},Rh={class:"px-3 py-2 text-sm text-gray-700"},Ah={class:"font-medium"},Oh={class:"text-gray-500"},Th=vs({__name:"AppHeader",setup(e){const t=lh(),n=jr(),s=Ot(!1),r=Ot(!1),o=Ot(),i=Te(()=>{var c,f;return(((c=n.user)==null?void 0:c.name)||((f=n.user)==null?void 0:f.email)||"").split(" ").map(p=>p[0]).join("").toUpperCase().slice(0,2)}),l=()=>{n.logout(),s.value=!1,r.value=!1,t.push("/")},a=u=>{o.value&&!o.value.contains(u.target)&&(s.value=!1)};return Pr(()=>{document.addEventListener("click",a)}),Ir(()=>{document.removeEventListener("click",a)}),(u,c)=>{var p,m,b,x,S,C;const f=Iu("router-link");return Ae(),ke("header",ch,[X("div",ah,[X("div",uh,[X("div",fh,[W(f,{to:"/",class:"flex items-center space-x-2"},{default:le(()=>c[2]||(c[2]=[X("div",{class:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center"},[X("span",{class:"text-white font-bold text-lg"},"G")],-1),X("span",{class:"text-xl font-bold text-gray-900"},"Kigali GasGo",-1)])),_:1,__:[2]})]),X("nav",dh,[he(n).isAuthenticated?(Ae(),ke(ge,{key:1},[he(n).userRole==="buyer"?(Ae(),ke(ge,{key:0},[W(f,{to:"/buyer/dashboard",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[6]||(c[6]=[ue(" Browse Gas ")])),_:1,__:[6]}),W(f,{to:"/buyer/orders",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[7]||(c[7]=[ue(" My Orders ")])),_:1,__:[7]})],64)):bt("",!0),he(n).userRole==="seller"?(Ae(),ke(ge,{key:1},[W(f,{to:"/seller/dashboard",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[8]||(c[8]=[ue(" Dashboard ")])),_:1,__:[8]}),W(f,{to:"/seller/inventory",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[9]||(c[9]=[ue(" Inventory ")])),_:1,__:[9]}),W(f,{to:"/seller/orders",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[10]||(c[10]=[ue(" Orders ")])),_:1,__:[10]})],64)):bt("",!0),he(n).userRole==="admin"?(Ae(),ke(ge,{key:2},[W(f,{to:"/admin/dashboard",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[11]||(c[11]=[ue(" Dashboard ")])),_:1,__:[11]}),W(f,{to:"/admin/orders",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[12]||(c[12]=[ue(" Orders ")])),_:1,__:[12]}),W(f,{to:"/admin/invoices",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[13]||(c[13]=[ue(" Invoices ")])),_:1,__:[13]}),W(f,{to:"/admin/users",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[14]||(c[14]=[ue(" Users ")])),_:1,__:[14]})],64)):bt("",!0),X("div",{class:"relative",ref_key:"userMenuRef",ref:o},[X("button",{onClick:c[0]||(c[0]=O=>s.value=!s.value),class:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"},[X("div",hh,[X("span",ph,vt(i.value),1)]),X("span",mh,vt(((p=he(n).user)==null?void 0:p.name)||((m=he(n).user)==null?void 0:m.email)),1),c[15]||(c[15]=X("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[X("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1))]),s.value?(Ae(),ke("div",gh,[X("div",yh,[X("div",bh,vt(((b=he(n).user)==null?void 0:b.name)||"User"),1),X("div",_h,vt((x=he(n).user)==null?void 0:x.email),1),X("div",vh,vt(he(n).userRole),1)]),X("button",{onClick:l,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"}," Sign out ")])):bt("",!0)],512)],64)):(Ae(),ke(ge,{key:0},[W(f,{to:"/",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[3]||(c[3]=[ue(" Home ")])),_:1,__:[3]}),W(f,{to:"/login",class:"text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[4]||(c[4]=[ue(" Login ")])),_:1,__:[4]}),W(f,{to:"/signup",class:"btn-primary"},{default:le(()=>c[5]||(c[5]=[ue(" Sign Up ")])),_:1,__:[5]})],64))]),X("div",wh,[X("button",{onClick:c[1]||(c[1]=O=>r.value=!r.value),class:"text-gray-600 hover:text-gray-900 transition-colors"},c[16]||(c[16]=[X("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[X("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])]),r.value?(Ae(),ke("div",xh,[X("div",Eh,[he(n).isAuthenticated?(Ae(),ke(ge,{key:1},[he(n).userRole==="buyer"?(Ae(),ke(ge,{key:0},[W(f,{to:"/buyer/dashboard",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[20]||(c[20]=[ue(" Browse Gas ")])),_:1,__:[20]}),W(f,{to:"/buyer/orders",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[21]||(c[21]=[ue(" My Orders ")])),_:1,__:[21]})],64)):bt("",!0),he(n).userRole==="seller"?(Ae(),ke(ge,{key:1},[W(f,{to:"/seller/dashboard",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[22]||(c[22]=[ue(" Dashboard ")])),_:1,__:[22]}),W(f,{to:"/seller/inventory",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[23]||(c[23]=[ue(" Inventory ")])),_:1,__:[23]}),W(f,{to:"/seller/orders",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[24]||(c[24]=[ue(" Orders ")])),_:1,__:[24]})],64)):bt("",!0),he(n).userRole==="admin"?(Ae(),ke(ge,{key:2},[W(f,{to:"/admin/dashboard",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[25]||(c[25]=[ue(" Dashboard ")])),_:1,__:[25]}),W(f,{to:"/admin/orders",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[26]||(c[26]=[ue(" Orders ")])),_:1,__:[26]}),W(f,{to:"/admin/invoices",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[27]||(c[27]=[ue(" Invoices ")])),_:1,__:[27]}),W(f,{to:"/admin/users",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[28]||(c[28]=[ue(" Users ")])),_:1,__:[28]})],64)):bt("",!0),X("div",Sh,[X("div",Rh,[X("div",Ah,vt(((S=he(n).user)==null?void 0:S.name)||"User"),1),X("div",Oh,vt((C=he(n).user)==null?void 0:C.email),1)]),X("button",{onClick:l,class:"block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"}," Sign out ")])],64)):(Ae(),ke(ge,{key:0},[W(f,{to:"/",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[17]||(c[17]=[ue(" Home ")])),_:1,__:[17]}),W(f,{to:"/login",class:"block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"},{default:le(()=>c[18]||(c[18]=[ue(" Login ")])),_:1,__:[18]}),W(f,{to:"/signup",class:"block px-3 py-2 text-primary-600 hover:text-primary-700 transition-colors"},{default:le(()=>c[19]||(c[19]=[ue(" Sign Up ")])),_:1,__:[19]})],64))])])):bt("",!0)])])}}}),Ch={id:"app",class:"min-h-screen bg-gray-50"},Ph={class:"flex-1"},Ih=vs({__name:"App",setup(e){const t=jr();return Pr(()=>{t.initializeAuth()}),(n,s)=>(Ae(),ke("div",Ch,[W(Th),X("main",Ph,[W(he(Jl))])]))}}),kh=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Nh=kh(Ih,[["__scopeId","data-v-390f0397"]]),Lh="modulepreload",Mh=function(e){return"/"+e},ei={},Se=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let i=function(u){return Promise.all(u.map(c=>Promise.resolve(c).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));r=i(n.map(u=>{if(u=Mh(u),u in ei)return;ei[u]=!0;const c=u.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const p=document.createElement("link");if(p.rel=c?"stylesheet":Lh,c||(p.as="script"),p.crossOrigin="",p.href=u,a&&p.setAttribute("nonce",a),document.head.appendChild(p),c)return new Promise((m,b)=>{p.addEventListener("load",m),p.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})},Gl=oh({history:Md("/"),routes:[{path:"/",name:"home",component:()=>Se(()=>import("./HomeView-B-H6TYbM.js"),[])},{path:"/login",name:"login",component:()=>Se(()=>import("./LoginView-BPShTVSv.js"),[]),meta:{requiresGuest:!0}},{path:"/signup",name:"signup",component:()=>Se(()=>import("./SignupView-BTZF12eg.js"),[]),meta:{requiresGuest:!0}},{path:"/buyer",name:"buyer",redirect:"/buyer/dashboard",meta:{requiresAuth:!0,role:"buyer"}},{path:"/buyer/dashboard",name:"buyer-dashboard",component:()=>Se(()=>import("./DashboardView-uu8MgaVP.js"),[]),meta:{requiresAuth:!0,role:"buyer"}},{path:"/buyer/order",name:"buyer-order",component:()=>Se(()=>import("./OrderView-BOfdkcty.js"),[]),meta:{requiresAuth:!0,role:"buyer"}},{path:"/buyer/orders",name:"buyer-orders",component:()=>Se(()=>import("./OrdersView-CGKJNxRd.js"),[]),meta:{requiresAuth:!0,role:"buyer"}},{path:"/buyer/ratings/order/:id",name:"buyer-rating",component:()=>Se(()=>import("./RatingView-BQtxxf2R.js"),[]),meta:{requiresAuth:!0,role:"buyer"}},{path:"/seller",name:"seller",redirect:"/seller/dashboard",meta:{requiresAuth:!0,role:"seller"}},{path:"/seller/dashboard",name:"seller-dashboard",component:()=>Se(()=>import("./DashboardView-Caf62GeQ.js"),[]),meta:{requiresAuth:!0,role:"seller"}},{path:"/seller/inventory",name:"seller-inventory",component:()=>Se(()=>import("./InventoryView-ssjQ361X.js"),[]),meta:{requiresAuth:!0,role:"seller"}},{path:"/seller/orders",name:"seller-orders",component:()=>Se(()=>import("./OrdersView-C8XGtDWC.js"),[]),meta:{requiresAuth:!0,role:"seller"}},{path:"/seller/invoice/generate/:id",name:"seller-invoice",component:()=>Se(()=>import("./InvoiceView-D5zD3EXu.js"),[]),meta:{requiresAuth:!0,role:"seller"}},{path:"/admin",name:"admin",redirect:"/admin/dashboard",meta:{requiresAuth:!0,role:"admin"}},{path:"/admin/dashboard",name:"admin-dashboard",component:()=>Se(()=>import("./DashboardView-Qc8ML5OM.js"),[]),meta:{requiresAuth:!0,role:"admin"}},{path:"/admin/orders",name:"admin-orders",component:()=>Se(()=>import("./OrdersView-B0nDFIE-.js"),[]),meta:{requiresAuth:!0,role:"admin"}},{path:"/admin/invoices",name:"admin-invoices",component:()=>Se(()=>import("./InvoicesView-VVXwSzQX.js"),[]),meta:{requiresAuth:!0,role:"admin"}},{path:"/admin/users",name:"admin-users",component:()=>Se(()=>import("./UsersView-Bhw-b7Wx.js"),[]),meta:{requiresAuth:!0,role:"admin"}}]});Gl.beforeEach((e,t,n)=>{const s=jr();if(e.meta.requiresAuth){if(!s.isAuthenticated){n({name:"login"});return}if(e.meta.role&&s.userRole!==e.meta.role){n({name:{buyer:"buyer-dashboard",seller:"seller-dashboard",admin:"admin-dashboard"}[s.userRole]||"home"});return}}if(e.meta.requiresGuest&&s.isAuthenticated){n({name:{buyer:"buyer-dashboard",seller:"seller-dashboard",admin:"admin-dashboard"}[s.userRole]||"home"});return}n()});const Dr=Gf(Nh);Dr.use(Yf());Dr.use(Gl);Dr.mount("#app");export{he as A,ge as F,X as a,np as b,ke as c,vs as d,ue as e,W as f,lh as g,Ot as h,kn as i,lp as j,bt as k,ep as l,rp as m,Pr as n,Ae as o,cp as p,br as q,Iu as r,ip as s,vt as t,jr as u,sp as v,le as w,Te as x,tp as y,op as z};
