import{d as x,h as p,n as y,c as r,b as v,a as t,k as n,F as b,y as h,f as _,w as f,r as w,t as a,q as A,e as k,g as C,o as d}from"./index-CC71qafK.js";const S={class:"min-h-screen bg-gray-50"},D={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},N={class:"space-y-6"},T={class:"p-6"},K={class:"flex items-center justify-between mb-4"},V={class:"text-lg font-semibold text-gray-900"},G={class:"text-sm text-gray-600"},P={class:"text-right"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4"},O={class:"text-sm text-gray-900"},Z={class:"text-sm text-gray-900"},B={class:"text-sm text-gray-900"},L={class:"text-sm text-gray-900"},R={class:"flex items-center justify-between pt-4 border-t border-gray-200"},j={class:"flex space-x-3"},F=["onClick"],q={class:"text-sm text-gray-500"},z={key:0,class:"text-center py-12"},E={class:"mt-6"},W=x({__name:"OrdersView",setup(H){const u=C(),i=p([{id:"001",product:{brand:"Meru",weight:"12 KG"},sellerName:"Kigali Gas Supplies",totalAmount:15e3,deliveryAddress:"Gasabo, Kigali",status:"Delivered",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-16T14:20:00Z"},{id:"002",product:{brand:"SP",weight:"15 KG"},sellerName:"City Gas Store",totalAmount:18e3,deliveryAddress:"Kicukiro, Kigali",status:"Out for Delivery",createdAt:"2024-01-20T09:15:00Z",updatedAt:"2024-01-20T16:45:00Z"},{id:"003",product:{brand:"Total",weight:"6 KG"},sellerName:"Quick Gas Rwanda",totalAmount:8e3,deliveryAddress:"Nyarugenge, Kigali",status:"Pending Admin Approval",createdAt:"2024-01-22T11:00:00Z",updatedAt:"2024-01-22T11:00:00Z"}]),m=o=>({"Pending Admin Approval":"bg-yellow-100 text-yellow-800","Payment Confirmed":"bg-blue-100 text-blue-800","Out for Delivery":"bg-purple-100 text-purple-800",Delivered:"bg-green-100 text-green-800",Cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",l=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=o=>{u.push({name:"buyer-rating",params:{id:o}})};return y(()=>{console.log("Loading orders...")}),(o,e)=>{const g=w("router-link");return d(),r("div",S,[e[9]||(e[9]=v('<div class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="py-6"><h1 class="text-3xl font-bold text-gray-900">My Orders</h1><p class="mt-2 text-gray-600">Track your gas delivery orders</p></div></div></div>',1)),t("div",D,[t("div",N,[(d(!0),r(b,null,h(i.value,s=>(d(),r("div",{key:s.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"},[t("div",T,[t("div",K,[t("div",null,[t("h3",V,"Order #"+a(s.id),1),t("p",G,"Placed on "+a(l(s.createdAt)),1)]),t("div",P,[t("span",{class:A(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",m(s.status)])},a(s.status),3)])]),t("div",M,[t("div",null,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-gray-700"},"Product",-1)),t("p",O,a(s.product.brand)+" - "+a(s.product.weight),1)]),t("div",null,[e[1]||(e[1]=t("p",{class:"text-sm font-medium text-gray-700"},"Seller",-1)),t("p",Z,a(s.sellerName),1)]),t("div",null,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-700"},"Total Amount",-1)),t("p",B,"RWF "+a(s.totalAmount.toLocaleString()),1)]),t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-700"},"Delivery Address",-1)),t("p",L,a(s.deliveryAddress),1)])]),t("div",R,[t("div",j,[s.status==="Delivered"?(d(),r("button",{key:0,onClick:Q=>c(s.id),class:"text-sm text-primary-600 hover:text-primary-700 font-medium"}," Rate Seller ",8,F)):n("",!0),e[4]||(e[4]=t("button",{class:"text-sm text-gray-600 hover:text-gray-700 font-medium"}," View Details ",-1))]),t("div",q," Last updated: "+a(l(s.updatedAt)),1)])])]))),128))]),i.value.length===0?(d(),r("div",z,[e[6]||(e[6]=t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)),e[7]||(e[7]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No orders yet",-1)),e[8]||(e[8]=t("p",{class:"mt-1 text-sm text-gray-500"},"Start by browsing our gas products.",-1)),t("div",E,[_(g,{to:"/buyer/dashboard",class:"btn-primary"},{default:f(()=>e[5]||(e[5]=[k(" Browse Products ")])),_:1,__:[5]})])])):n("",!0)])])}}});export{W as default};
