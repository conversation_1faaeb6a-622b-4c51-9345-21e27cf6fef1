import{d as e,c as a,b as s,o as r}from"./index-CC71qafK.js";const d={class:"min-h-screen bg-gray-50"},n=e({__name:"InventoryView",setup(i){return(x,t)=>(r(),a("div",d,t[0]||(t[0]=[s('<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="flex justify-between items-center mb-8"><h1 class="text-3xl font-bold text-gray-900">Inventory Management</h1><button class="btn-primary">Add New Stock</button></div><div class="bg-white rounded-lg shadow overflow-hidden"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Meru</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12 KG</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">25</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">RWF 15,000</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-primary-600 hover:text-primary-900 mr-3">Edit</button><button class="text-red-600 hover:text-red-900">Remove</button></td></tr></tbody></table></div></div>',1)])))}});export{n as default};
