import{d as G,h as b,i as N,x,n as V,c as n,b as u,a as t,k as h,l as d,v as L,s as m,F as M,y as K,t as i,g as j,o as r,e as B,q as F}from"./index-CC71qafK.js";const T={class:"min-h-screen bg-gray-50"},P={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},q={class:"bg-white rounded-lg shadow-sm p-6 mb-8"},A={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},O={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},W={class:"p-4"},z={class:"flex items-center justify-between mb-2"},R={class:"text-lg font-semibold text-gray-900"},U={class:"text-sm font-medium text-primary-600"},D={class:"text-2xl font-bold text-gray-900 mb-2"},H={class:"text-sm text-gray-600 mb-3"},I={class:"font-medium"},$={class:"flex items-center"},E={class:"flex items-center justify-between mb-4"},Q={class:"text-sm text-gray-500"},J={class:"space-y-2"},X=["onClick","disabled"],Y=["onClick","disabled"],Z={key:0,class:"text-center py-12"},tt={key:0,class:"fixed bottom-4 right-4"},et={class:"bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm"},ot={class:"flex items-center justify-between mb-2"},st={class:"font-semibold text-gray-900"},at={class:"text-sm text-gray-600"},nt=G({__name:"DashboardView",setup(it){const g=j(),v=b([{id:"1",brand:"Meru",weight:"12 KG",price:15e3,sellerName:"Kigali Gas Supplies",location:"Gasabo",inStock:!0,quantity:25},{id:"2",brand:"SP",weight:"15 KG",price:18e3,sellerName:"City Gas Store",location:"Kicukiro",inStock:!0,quantity:12},{id:"3",brand:"Total",weight:"6 KG",price:8e3,sellerName:"Quick Gas Rwanda",location:"Nyarugenge",inStock:!1,quantity:0},{id:"4",brand:"Oryx",weight:"22 KG",price:25e3,sellerName:"Premium Gas Ltd",location:"Gasabo",inStock:!0,quantity:8}]),s=N({search:"",brand:"",weight:"",location:""}),l=b([]),p=x(()=>v.value.filter(a=>{const e=!s.search||a.brand.toLowerCase().includes(s.search.toLowerCase())||a.sellerName.toLowerCase().includes(s.search.toLowerCase()),o=!s.brand||a.brand===s.brand,c=!s.weight||a.weight.includes(s.weight),_=!s.location||a.location===s.location;return e&&o&&c&&_})),y=x(()=>l.value.reduce((a,e)=>a+e.price,0)),w=()=>{s.search="",s.brand="",s.weight="",s.location=""},f=()=>{console.log("Filters applied:",s)},k=a=>{l.value.push(a)},C=a=>{l.value.push(a),g.push({name:"buyer-order"})},S=()=>{g.push({name:"buyer-order"})};return V(()=>{console.log("Loading products...")}),(a,e)=>(r(),n("div",T,[e[15]||(e[15]=u('<div class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="py-6"><h1 class="text-3xl font-bold text-gray-900">Browse Gas Products</h1><p class="mt-2 text-gray-600">Find and order cooking gas from verified suppliers in your area</p></div></div></div>',1)),t("div",P,[t("div",q,[e[11]||(e[11]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Filter Products",-1)),t("div",A,[t("div",null,[e[4]||(e[4]=t("label",{for:"search",class:"block text-sm font-medium text-gray-700 mb-1"}," Search ",-1)),d(t("input",{id:"search","onUpdate:modelValue":e[0]||(e[0]=o=>s.search=o),type:"text",class:"input-field",placeholder:"Search by brand or type..."},null,512),[[L,s.search]])]),t("div",null,[e[6]||(e[6]=t("label",{for:"brand",class:"block text-sm font-medium text-gray-700 mb-1"}," Brand ",-1)),d(t("select",{id:"brand","onUpdate:modelValue":e[1]||(e[1]=o=>s.brand=o),class:"input-field"},e[5]||(e[5]=[u('<option value="">All Brands</option><option value="Meru">Meru</option><option value="SP">SP</option><option value="Total">Total</option><option value="Oryx">Oryx</option>',5)]),512),[[m,s.brand]])]),t("div",null,[e[8]||(e[8]=t("label",{for:"weight",class:"block text-sm font-medium text-gray-700 mb-1"}," Weight ",-1)),d(t("select",{id:"weight","onUpdate:modelValue":e[2]||(e[2]=o=>s.weight=o),class:"input-field"},e[7]||(e[7]=[u('<option value="">All Weights</option><option value="6kg">6 KG</option><option value="12kg">12 KG</option><option value="15kg">15 KG</option><option value="22kg">22 KG</option>',5)]),512),[[m,s.weight]])]),t("div",null,[e[10]||(e[10]=t("label",{for:"location",class:"block text-sm font-medium text-gray-700 mb-1"}," Seller Location ",-1)),d(t("select",{id:"location","onUpdate:modelValue":e[3]||(e[3]=o=>s.location=o),class:"input-field"},e[9]||(e[9]=[t("option",{value:""},"All Locations",-1),t("option",{value:"Gasabo"},"Gasabo",-1),t("option",{value:"Kicukiro"},"Kicukiro",-1),t("option",{value:"Nyarugenge"},"Nyarugenge",-1)]),512),[[m,s.location]])])]),t("div",{class:"mt-4 flex justify-between items-center"},[t("button",{onClick:w,class:"text-sm text-gray-500 hover:text-gray-700 transition-colors"}," Clear all filters "),t("button",{onClick:f,class:"btn-primary"}," Apply Filters ")])]),t("div",O,[(r(!0),n(M,null,K(p.value,o=>(r(),n("div",{key:o.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"},[e[13]||(e[13]=t("div",{class:"h-48 bg-gray-100 flex items-center justify-center"},[t("svg",{class:"w-16 h-16 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1)),t("div",W,[t("div",z,[t("h3",R,i(o.brand),1),t("span",U,i(o.weight),1)]),t("p",D,"RWF "+i(o.price.toLocaleString()),1),t("div",H,[t("p",I,i(o.sellerName),1),t("p",$,[e[12]||(e[12]=t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),B(" "+i(o.location),1)])]),t("div",E,[t("span",{class:F(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",o.inStock?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},i(o.inStock?"In Stock":"Out of Stock"),3),t("span",Q,i(o.quantity)+" available",1)]),t("div",J,[t("button",{onClick:c=>k(o),disabled:!o.inStock,class:"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"}," Add to Cart ",8,X),t("button",{onClick:c=>C(o),disabled:!o.inStock,class:"w-full btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"}," Order Now ",8,Y)])])]))),128))]),p.value.length===0?(r(),n("div",Z,e[14]||(e[14]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No products found",-1),t("p",{class:"mt-1 text-sm text-gray-500"},"Try adjusting your filters or search terms.",-1)]))):h("",!0)]),l.value.length>0?(r(),n("div",tt,[t("div",et,[t("div",ot,[t("h3",st,"Cart ("+i(l.value.length)+")",1),t("button",{onClick:S,class:"text-primary-600 hover:text-primary-700 text-sm font-medium"}," View Cart ")]),t("p",at," Total: RWF "+i(y.value.toLocaleString()),1)])])):h("",!0)]))}});export{nt as default};
