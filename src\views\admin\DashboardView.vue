<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Admin Dashboard</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Total Users</h3>
          <p class="text-3xl font-bold text-primary-600 mt-2">1,234</p>
          <p class="text-sm text-gray-600">Registered users</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Active Sellers</h3>
          <p class="text-3xl font-bold text-secondary-600 mt-2">89</p>
          <p class="text-sm text-gray-600">Verified sellers</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Pending Orders</h3>
          <p class="text-3xl font-bold text-accent-600 mt-2">23</p>
          <p class="text-sm text-gray-600">Awaiting approval</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900">Total Revenue</h3>
          <p class="text-3xl font-bold text-green-600 mt-2">RWF 2.4M</p>
          <p class="text-sm text-gray-600">This month</p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <router-link to="/admin/orders" class="block btn-primary text-center">
              Manage Orders
            </router-link>
            <router-link to="/admin/invoices" class="block btn-secondary text-center">
              Review Invoices
            </router-link>
            <router-link to="/admin/users" class="block btn-secondary text-center">
              Manage Users
            </router-link>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span>New seller registered</span>
              <span class="text-gray-500">1 hour ago</span>
            </div>
            <div class="flex justify-between">
              <span>Order approved</span>
              <span class="text-gray-500">3 hours ago</span>
            </div>
            <div class="flex justify-between">
              <span>Invoice processed</span>
              <span class="text-gray-500">5 hours ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Admin dashboard logic will be implemented here
</script>
