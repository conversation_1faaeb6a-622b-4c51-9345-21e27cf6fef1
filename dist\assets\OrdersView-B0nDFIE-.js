import{d as e,c as s,b as a,o as r}from"./index-CC71qafK.js";const p={class:"min-h-screen bg-gray-50"},l=e({__name:"OrdersView",setup(x){return(d,t)=>(r(),s("div",p,t[0]||(t[0]=[a('<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><h1 class="text-3xl font-bold text-gray-900 mb-8">Manage Orders</h1><div class="bg-white rounded-lg shadow overflow-hidden"><div class="px-6 py-4 border-b border-gray-200"><h2 class="text-lg font-semibold text-gray-900">Orders Requiring Approval</h2></div><div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buyer</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seller</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#001</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">John Buyer</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Kigali Gas Supplies</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Meru 12KG</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">RWF 15,000</td><td class="px-6 py-4 whitespace-nowrap"><span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"> Pending </span></td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-green-600 hover:text-green-900 mr-3">Approve</button><button class="text-red-600 hover:text-red-900">Reject</button></td></tr></tbody></table></div></div></div>',1)])))}});export{l as default};
