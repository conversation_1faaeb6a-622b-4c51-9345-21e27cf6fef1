<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>er -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900">Rate Your Experience</h1>
          <p class="mt-2 text-gray-600">Help other customers by rating this seller</p>
        </div>
      </div>
    </div>

    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <!-- Order Summary -->
        <div class="border-b border-gray-200 pb-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Details</h2>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p class="font-medium text-gray-700">Order ID</p>
              <p class="text-gray-900">#{{ orderId }}</p>
            </div>
            <div>
              <p class="font-medium text-gray-700">Product</p>
              <p class="text-gray-900">{{ order.product.brand }} - {{ order.product.weight }}</p>
            </div>
            <div>
              <p class="font-medium text-gray-700">Seller</p>
              <p class="text-gray-900">{{ order.sellerName }}</p>
            </div>
            <div>
              <p class="font-medium text-gray-700">Delivery Date</p>
              <p class="text-gray-900">{{ formatDate(order.deliveredAt) }}</p>
            </div>
          </div>
        </div>

        <!-- Rating Form -->
        <form @submit.prevent="submitRating">
          <!-- Service Rating -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              Overall Service Rating
            </label>
            <div class="flex items-center space-x-2">
              <button
                v-for="star in 5"
                :key="`service-${star}`"
                type="button"
                @click="rating.service = star"
                :class="[
                  'w-8 h-8 transition-colors duration-200',
                  star <= rating.service
                    ? 'text-yellow-400 hover:text-yellow-500'
                    : 'text-gray-300 hover:text-gray-400'
                ]"
              >
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </button>
              <span class="ml-3 text-sm text-gray-600">
                {{ getRatingText(rating.service) }}
              </span>
            </div>
          </div>

          <!-- Delivery Rating -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              Delivery Experience
            </label>
            <div class="flex items-center space-x-2">
              <button
                v-for="star in 5"
                :key="`delivery-${star}`"
                type="button"
                @click="rating.delivery = star"
                :class="[
                  'w-8 h-8 transition-colors duration-200',
                  star <= rating.delivery
                    ? 'text-yellow-400 hover:text-yellow-500'
                    : 'text-gray-300 hover:text-gray-400'
                ]"
              >
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </button>
              <span class="ml-3 text-sm text-gray-600">
                {{ getRatingText(rating.delivery) }}
              </span>
            </div>
          </div>

          <!-- Comments -->
          <div class="mb-6">
            <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">
              Additional Comments (Optional)
            </label>
            <textarea
              id="comments"
              v-model="rating.comments"
              rows="4"
              class="input-field"
              placeholder="Share your experience with other customers..."
            ></textarea>
          </div>

          <!-- Recommendation -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              Would you recommend this seller to others?
            </label>
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input
                  v-model="rating.recommend"
                  type="radio"
                  :value="true"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">Yes</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="rating.recommend"
                  type="radio"
                  :value="false"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">No</span>
              </label>
            </div>
          </div>

          <!-- Error message -->
          <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p class="text-sm text-red-800">{{ error }}</p>
          </div>

          <!-- Success message -->
          <div v-if="success" class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <p class="text-sm text-green-800">{{ success }}</p>
          </div>

          <!-- Submit Button -->
          <div class="flex space-x-4">
            <button
              type="submit"
              :disabled="loading || !canSubmit"
              class="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading">Submitting...</span>
              <span v-else>Submit Rating</span>
            </button>
            <button
              type="button"
              @click="goBack"
              class="flex-1 btn-secondary"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const orderId = route.params.id as string

interface Order {
  id: string
  product: {
    brand: string
    weight: string
  }
  sellerName: string
  deliveredAt: string
}

const order = ref<Order>({
  id: orderId,
  product: {
    brand: 'Meru',
    weight: '12 KG'
  },
  sellerName: 'Kigali Gas Supplies',
  deliveredAt: '2024-01-16T14:20:00Z'
})

const loading = ref(false)
const error = ref('')
const success = ref('')

const rating = reactive({
  service: 0,
  delivery: 0,
  comments: '',
  recommend: null as boolean | null
})

const canSubmit = computed(() => {
  return rating.service > 0 && rating.delivery > 0 && rating.recommend !== null
})

const getRatingText = (stars: number) => {
  const texts = {
    0: 'No rating',
    1: 'Poor',
    2: 'Fair',
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent'
  }
  return texts[stars as keyof typeof texts] || 'No rating'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const submitRating = async () => {
  if (!canSubmit.value) {
    error.value = 'Please provide ratings for both service and delivery'
    return
  }

  loading.value = true
  error.value = ''
  success.value = ''

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    success.value = 'Thank you for your feedback! Your rating has been submitted.'
    
    // Redirect after a short delay
    setTimeout(() => {
      router.push({ name: 'buyer-orders' })
    }, 2000)
  } catch (err) {
    error.value = 'Failed to submit rating. Please try again.'
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'buyer-orders' })
}

onMounted(() => {
  // Load order details from API
  console.log('Loading order details for:', orderId)
})
</script>
