<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-3xl font-bold text-gray-900">My Orders</h1>
          <p class="mt-2 text-gray-600">Track your gas delivery orders</p>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Orders List -->
      <div class="space-y-6">
        <div
          v-for="order in orders"
          :key="order.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Order #{{ order.id }}</h3>
                <p class="text-sm text-gray-600">Placed on {{ formatDate(order.createdAt) }}</p>
              </div>
              <div class="text-right">
                <span
                  :class="[
                    'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                    getStatusColor(order.status)
                  ]"
                >
                  {{ order.status }}
                </span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <p class="text-sm font-medium text-gray-700">Product</p>
                <p class="text-sm text-gray-900">{{ order.product.brand }} - {{ order.product.weight }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Seller</p>
                <p class="text-sm text-gray-900">{{ order.sellerName }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Total Amount</p>
                <p class="text-sm text-gray-900">RWF {{ order.totalAmount.toLocaleString() }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700">Delivery Address</p>
                <p class="text-sm text-gray-900">{{ order.deliveryAddress }}</p>
              </div>
            </div>

            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
              <div class="flex space-x-3">
                <button
                  v-if="order.status === 'Delivered'"
                  @click="rateOrder(order.id)"
                  class="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  Rate Seller
                </button>
                <button
                  class="text-sm text-gray-600 hover:text-gray-700 font-medium"
                >
                  View Details
                </button>
              </div>
              <div class="text-sm text-gray-500">
                Last updated: {{ formatDate(order.updatedAt) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="orders.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
        <p class="mt-1 text-sm text-gray-500">Start by browsing our gas products.</p>
        <div class="mt-6">
          <router-link to="/buyer/dashboard" class="btn-primary">
            Browse Products
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface Order {
  id: string
  product: {
    brand: string
    weight: string
  }
  sellerName: string
  totalAmount: number
  deliveryAddress: string
  status: string
  createdAt: string
  updatedAt: string
}

// Mock orders data
const orders = ref<Order[]>([
  {
    id: '001',
    product: {
      brand: 'Meru',
      weight: '12 KG'
    },
    sellerName: 'Kigali Gas Supplies',
    totalAmount: 15000,
    deliveryAddress: 'Gasabo, Kigali',
    status: 'Delivered',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-16T14:20:00Z'
  },
  {
    id: '002',
    product: {
      brand: 'SP',
      weight: '15 KG'
    },
    sellerName: 'City Gas Store',
    totalAmount: 18000,
    deliveryAddress: 'Kicukiro, Kigali',
    status: 'Out for Delivery',
    createdAt: '2024-01-20T09:15:00Z',
    updatedAt: '2024-01-20T16:45:00Z'
  },
  {
    id: '003',
    product: {
      brand: 'Total',
      weight: '6 KG'
    },
    sellerName: 'Quick Gas Rwanda',
    totalAmount: 8000,
    deliveryAddress: 'Nyarugenge, Kigali',
    status: 'Pending Admin Approval',
    createdAt: '2024-01-22T11:00:00Z',
    updatedAt: '2024-01-22T11:00:00Z'
  }
])

const getStatusColor = (status: string) => {
  const colors = {
    'Pending Admin Approval': 'bg-yellow-100 text-yellow-800',
    'Payment Confirmed': 'bg-blue-100 text-blue-800',
    'Out for Delivery': 'bg-purple-100 text-purple-800',
    'Delivered': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const rateOrder = (orderId: string) => {
  router.push({ name: 'buyer-rating', params: { id: orderId } })
}

onMounted(() => {
  // Load orders from API
  console.log('Loading orders...')
})
</script>
