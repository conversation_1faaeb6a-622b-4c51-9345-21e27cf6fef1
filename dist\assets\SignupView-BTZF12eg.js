import{d as P,u as C,h as f,i as N,n as V,p as L,c as d,a as e,e as o,f as q,w as M,r as B,j,k as c,q as y,l as n,v as l,F as S,s as U,m as E,t as x,g as z,o as m}from"./index-CC71qafK.js";const R={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},T={class:"max-w-md w-full space-y-8"},F={class:"mt-2 text-center text-sm text-gray-600"},A={class:"grid grid-cols-2 gap-3"},D={class:"space-y-4"},G={key:0},H={class:"flex items-center"},I={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4"},K={class:"flex"},$={class:"ml-3"},O={class:"text-sm text-red-800"},J=["disabled"],Q={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Y=P({__name:"SignupView",setup(W){const v=z(),p=L(),g=C(),i=f(!1),a=f(""),t=N({email:"",password:"",confirmPassword:"",role:"buyer",name:"",businessName:"",businessPhone:"",businessLocation:"",acceptTerms:!1});V(()=>{p.query.role&&(p.query.role==="buyer"||p.query.role==="seller")&&(t.role=p.query.role)});const w=()=>!t.email||!t.password||!t.confirmPassword?"Please fill in all required fields":t.password!==t.confirmPassword?"Passwords do not match":t.password.length<6?"Password must be at least 6 characters long":t.role==="seller"&&(!t.businessName||!t.name||!t.businessPhone||!t.businessLocation)?"Please fill in all business information":t.acceptTerms?null:"Please accept the terms and conditions",k=async()=>{const b=w();if(b){a.value=b;return}i.value=!0,a.value="";try{const s={email:t.email,password:t.password,role:t.role,...t.name&&{name:t.name},...t.businessName&&{businessName:t.businessName},...t.businessPhone&&{businessPhone:t.businessPhone},...t.businessLocation&&{businessLocation:t.businessLocation}},u=await g.register(s);if(u.success){const h={buyer:"buyer-dashboard",seller:"seller-dashboard",admin:"admin-dashboard"}[u.user.role];v.push({name:h})}else a.value=u.error||"Registration failed"}catch(s){a.value="An unexpected error occurred",console.error("Registration error:",s)}finally{i.value=!1}};return(b,s)=>{const u=B("router-link");return m(),d("div",R,[e("div",T,[e("div",null,[s[13]||(s[13]=e("div",{class:"flex justify-center"},[e("div",{class:"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center"},[e("span",{class:"text-white font-bold text-xl"},"G")])],-1)),s[14]||(s[14]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Create your account ",-1)),e("p",F,[s[12]||(s[12]=o(" Or ")),q(u,{to:"/login",class:"font-medium text-primary-600 hover:text-primary-500"},{default:M(()=>s[11]||(s[11]=[o(" sign in to your existing account ")])),_:1,__:[11]})])]),e("form",{class:"mt-8 space-y-6",onSubmit:j(k,["prevent"])},[e("div",null,[s[17]||(s[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-3"}," I am a: ",-1)),e("div",A,[e("button",{type:"button",onClick:s[0]||(s[0]=r=>t.role="buyer"),class:y(["flex items-center justify-center px-4 py-3 border rounded-lg text-sm font-medium transition-colors duration-200",t.role==="buyer"?"border-primary-500 bg-primary-50 text-primary-700":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"])},s[15]||(s[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),o(" Buyer ")]),2),e("button",{type:"button",onClick:s[1]||(s[1]=r=>t.role="seller"),class:y(["flex items-center justify-center px-4 py-3 border rounded-lg text-sm font-medium transition-colors duration-200",t.role==="seller"?"border-primary-500 bg-primary-50 text-primary-700":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"])},s[16]||(s[16]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1),o(" Seller ")]),2)])]),e("div",D,[e("div",null,[s[18]||(s[18]=e("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Email address ",-1)),n(e("input",{id:"email","onUpdate:modelValue":s[2]||(s[2]=r=>t.email=r),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"Enter your email"},null,512),[[l,t.email]])]),e("div",null,[s[19]||(s[19]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Password ",-1)),n(e("input",{id:"password","onUpdate:modelValue":s[3]||(s[3]=r=>t.password=r),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Create a password"},null,512),[[l,t.password]])]),e("div",null,[s[20]||(s[20]=e("label",{for:"confirmPassword",class:"block text-sm font-medium text-gray-700"}," Confirm Password ",-1)),n(e("input",{id:"confirmPassword","onUpdate:modelValue":s[4]||(s[4]=r=>t.confirmPassword=r),name:"confirmPassword",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Confirm your password"},null,512),[[l,t.confirmPassword]])]),t.role==="buyer"?(m(),d("div",G,[s[21]||(s[21]=e("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Full Name ",-1)),n(e("input",{id:"name","onUpdate:modelValue":s[5]||(s[5]=r=>t.name=r),name:"name",type:"text",autocomplete:"name",class:"input-field mt-1",placeholder:"Enter your full name"},null,512),[[l,t.name]])])):c("",!0),t.role==="seller"?(m(),d(S,{key:1},[e("div",null,[s[22]||(s[22]=e("label",{for:"businessName",class:"block text-sm font-medium text-gray-700"},[o(" Business Name "),e("span",{class:"text-red-500"},"*")],-1)),n(e("input",{id:"businessName","onUpdate:modelValue":s[6]||(s[6]=r=>t.businessName=r),name:"businessName",type:"text",required:"",class:"input-field mt-1",placeholder:"Enter your business name"},null,512),[[l,t.businessName]])]),e("div",null,[s[23]||(s[23]=e("label",{for:"businessContact",class:"block text-sm font-medium text-gray-700"},[o(" Contact Person Name "),e("span",{class:"text-red-500"},"*")],-1)),n(e("input",{id:"businessContact","onUpdate:modelValue":s[7]||(s[7]=r=>t.name=r),name:"businessContact",type:"text",required:"",class:"input-field mt-1",placeholder:"Enter contact person name"},null,512),[[l,t.name]])]),e("div",null,[s[24]||(s[24]=e("label",{for:"businessPhone",class:"block text-sm font-medium text-gray-700"},[o(" Business Phone "),e("span",{class:"text-red-500"},"*")],-1)),n(e("input",{id:"businessPhone","onUpdate:modelValue":s[8]||(s[8]=r=>t.businessPhone=r),name:"businessPhone",type:"tel",required:"",class:"input-field mt-1",placeholder:"Enter business phone number"},null,512),[[l,t.businessPhone]])]),e("div",null,[s[26]||(s[26]=e("label",{for:"businessLocation",class:"block text-sm font-medium text-gray-700"},[o(" Business Location "),e("span",{class:"text-red-500"},"*")],-1)),n(e("select",{id:"businessLocation","onUpdate:modelValue":s[9]||(s[9]=r=>t.businessLocation=r),name:"businessLocation",required:"",class:"input-field mt-1"},s[25]||(s[25]=[e("option",{value:""},"Select district",-1),e("option",{value:"Gasabo"},"Gasabo",-1),e("option",{value:"Kicukiro"},"Kicukiro",-1),e("option",{value:"Nyarugenge"},"Nyarugenge",-1)]),512),[[U,t.businessLocation]])])],64)):c("",!0)]),e("div",H,[n(e("input",{id:"terms","onUpdate:modelValue":s[10]||(s[10]=r=>t.acceptTerms=r),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[E,t.acceptTerms]]),s[27]||(s[27]=e("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[o(" I agree to the "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Terms and Conditions"),o(" and "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Privacy Policy")],-1))]),a.value?(m(),d("div",I,[e("div",K,[s[28]||(s[28]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",$,[e("p",O,x(a.value),1)])])])):c("",!0),e("div",null,[e("button",{type:"submit",disabled:i.value,class:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[i.value?(m(),d("span",Q,s[29]||(s[29]=[e("svg",{class:"animate-spin h-5 w-5 text-primary-300",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):c("",!0),o(" "+x(i.value?"Creating account...":"Create account"),1)],8,J)])],32)])])}}});export{Y as default};
