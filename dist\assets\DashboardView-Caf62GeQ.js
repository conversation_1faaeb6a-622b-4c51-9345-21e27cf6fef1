import{d as r,c as n,a as t,b as a,f as o,w as d,r as i,e as l,o as c}from"./index-CC71qafK.js";const p={class:"min-h-screen bg-gray-50"},x={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},g={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},m={class:"bg-white rounded-lg shadow p-6"},v={class:"space-y-3"},f=r({__name:"DashboardView",setup(b){return(y,s)=>{const e=i("router-link");return c(),n("div",p,[t("div",x,[s[4]||(s[4]=a('<h1 class="text-3xl font-bold text-gray-900 mb-8">Seller Dashboard</h1><div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"><div class="bg-white rounded-lg shadow p-6"><h3 class="text-lg font-semibold text-gray-900">Current Stock</h3><p class="text-3xl font-bold text-primary-600 mt-2">45</p><p class="text-sm text-gray-600">Gas cylinders</p></div><div class="bg-white rounded-lg shadow p-6"><h3 class="text-lg font-semibold text-gray-900">Pending Orders</h3><p class="text-3xl font-bold text-accent-600 mt-2">8</p><p class="text-sm text-gray-600">Orders to process</p></div><div class="bg-white rounded-lg shadow p-6"><h3 class="text-lg font-semibold text-gray-900">Completed Deliveries</h3><p class="text-3xl font-bold text-secondary-600 mt-2">127</p><p class="text-sm text-gray-600">This month</p></div></div>',2)),t("div",g,[t("div",m,[s[2]||(s[2]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1)),t("div",v,[o(e,{to:"/seller/inventory",class:"block btn-primary text-center"},{default:d(()=>s[0]||(s[0]=[l(" Manage Inventory ")])),_:1,__:[0]}),o(e,{to:"/seller/orders",class:"block btn-secondary text-center"},{default:d(()=>s[1]||(s[1]=[l(" View Orders ")])),_:1,__:[1]})])]),s[3]||(s[3]=a('<div class="bg-white rounded-lg shadow p-6"><h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3><div class="space-y-3 text-sm"><div class="flex justify-between"><span>New order received</span><span class="text-gray-500">2 hours ago</span></div><div class="flex justify-between"><span>Stock updated</span><span class="text-gray-500">5 hours ago</span></div><div class="flex justify-between"><span>Order delivered</span><span class="text-gray-500">1 day ago</span></div></div></div>',1))])])])}}});export{f as default};
