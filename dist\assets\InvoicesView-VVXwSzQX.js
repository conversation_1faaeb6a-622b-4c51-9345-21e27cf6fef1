import{d as t,c as s,b as a,o}from"./index-CC71qafK.js";const i={class:"min-h-screen bg-gray-50"},d=t({__name:"InvoicesView",setup(r){return(n,e)=>(o(),s("div",i,e[0]||(e[0]=[a('<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><h1 class="text-3xl font-bold text-gray-900 mb-8">Manage Invoices</h1><div class="bg-white rounded-lg shadow overflow-hidden"><div class="px-6 py-4 border-b border-gray-200"><h2 class="text-lg font-semibold text-gray-900">Invoices Requiring Review</h2></div><div class="p-6"><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No invoices pending</h3><p class="mt-1 text-sm text-gray-500">Invoices will appear here when sellers submit them.</p></div></div></div></div>',1)])))}});export{d as default};
