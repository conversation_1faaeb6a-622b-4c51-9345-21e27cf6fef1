import{d as k,h as x,i as h,x as M,c as r,b as w,a as e,F as N,y as V,t as i,j as P,k as y,l as o,e as d,v as n,s as D,m as U,z as v,g as S,o as a}from"./index-CC71qafK.js";const A={class:"min-h-screen bg-gray-50"},C={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},F={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},B={class:"bg-white rounded-lg shadow-sm p-6"},R={class:"space-y-4"},T={class:"font-medium text-gray-900"},E={class:"text-sm text-gray-600"},G={class:"text-right"},K={class:"font-medium text-gray-900"},q={class:"mt-6 border-t border-gray-200 pt-4"},L={class:"flex justify-between text-lg font-semibold text-gray-900"},O={class:"bg-white rounded-lg shadow-sm p-6"},j={class:"flex items-center"},I={class:"mt-6"},W={class:"space-y-3"},Y={class:"flex items-center"},_={class:"flex items-center"},z={class:"flex items-center"},H={key:0,class:"mt-4"},J={key:1,class:"mt-4 space-y-3"},Q={class:"grid grid-cols-2 gap-3"},X={key:2,class:"mt-4 p-4 bg-gray-50 rounded-lg"},Z={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4"},$={class:"text-sm text-red-800"},ee={class:"mt-6"},te=["disabled"],se={key:0},le={key:1},ae=k({__name:"OrderView",setup(oe){const f=S(),b=x([{id:"1",brand:"Meru",weight:"12 KG",price:15e3,sellerName:"Kigali Gas Supplies"}]),p=x(!1),m=x(""),s=h({fullName:"",phone:"",address:"",district:"",saveAddress:!1,paymentMethod:"momo",momoPhone:"",cardNumber:"",expiryDate:"",cvv:""}),c=M(()=>b.value.reduce((u,t)=>u+t.price,0)),g=async()=>{if(!s.fullName||!s.phone||!s.address||!s.district){m.value="Please fill in all required fields";return}if(!s.paymentMethod){m.value="Please select a payment method";return}p.value=!0,m.value="";try{await new Promise(u=>setTimeout(u,2e3)),f.push({name:"buyer-orders"})}catch{m.value="Failed to place order. Please try again."}finally{p.value=!1}};return(u,t)=>(a(),r("div",A,[t[30]||(t[30]=w('<div class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="py-6"><h1 class="text-3xl font-bold text-gray-900">Place Order</h1><p class="mt-2 text-gray-600">Review your order and provide delivery details</p></div></div></div>',1)),e("div",C,[e("div",F,[e("div",B,[t[13]||(t[13]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Order Summary",-1)),e("div",R,[(a(!0),r(N,null,V(b.value,l=>(a(),r("div",{key:l.id,class:"flex items-center justify-between border-b border-gray-200 pb-4"},[e("div",null,[e("h3",T,i(l.brand)+" - "+i(l.weight),1),e("p",E,i(l.sellerName),1)]),e("div",G,[e("p",K,"RWF "+i(l.price.toLocaleString()),1)])]))),128))]),e("div",q,[e("div",L,[t[12]||(t[12]=e("span",null,"Total",-1)),e("span",null,"RWF "+i(c.value.toLocaleString()),1)])])]),e("div",O,[t[29]||(t[29]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Delivery Information",-1)),e("form",{onSubmit:P(g,["prevent"]),class:"space-y-4"},[e("div",null,[t[14]||(t[14]=e("label",{for:"fullName",class:"block text-sm font-medium text-gray-700"},[d(" Full Name "),e("span",{class:"text-red-500"},"*")],-1)),o(e("input",{id:"fullName","onUpdate:modelValue":t[0]||(t[0]=l=>s.fullName=l),type:"text",required:"",class:"input-field mt-1",placeholder:"Enter your full name"},null,512),[[n,s.fullName]])]),e("div",null,[t[15]||(t[15]=e("label",{for:"phone",class:"block text-sm font-medium text-gray-700"},[d(" Contact Phone "),e("span",{class:"text-red-500"},"*")],-1)),o(e("input",{id:"phone","onUpdate:modelValue":t[1]||(t[1]=l=>s.phone=l),type:"tel",required:"",class:"input-field mt-1",placeholder:"Enter your phone number"},null,512),[[n,s.phone]])]),e("div",null,[t[16]||(t[16]=e("label",{for:"address",class:"block text-sm font-medium text-gray-700"},[d(" Delivery Address "),e("span",{class:"text-red-500"},"*")],-1)),o(e("textarea",{id:"address","onUpdate:modelValue":t[2]||(t[2]=l=>s.address=l),rows:"3",required:"",class:"input-field mt-1",placeholder:"Enter your complete delivery address"},null,512),[[n,s.address]])]),e("div",null,[t[18]||(t[18]=e("label",{for:"district",class:"block text-sm font-medium text-gray-700"},[d(" District "),e("span",{class:"text-red-500"},"*")],-1)),o(e("select",{id:"district","onUpdate:modelValue":t[3]||(t[3]=l=>s.district=l),required:"",class:"input-field mt-1"},t[17]||(t[17]=[e("option",{value:""},"Select district",-1),e("option",{value:"Gasabo"},"Gasabo",-1),e("option",{value:"Kicukiro"},"Kicukiro",-1),e("option",{value:"Nyarugenge"},"Nyarugenge",-1)]),512),[[D,s.district]])]),e("div",j,[o(e("input",{id:"saveAddress","onUpdate:modelValue":t[4]||(t[4]=l=>s.saveAddress=l),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[U,s.saveAddress]]),t[19]||(t[19]=e("label",{for:"saveAddress",class:"ml-2 block text-sm text-gray-900"}," Save address for future use ",-1))]),e("div",I,[t[28]||(t[28]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Payment Method",-1)),e("div",W,[e("div",Y,[o(e("input",{id:"momo","onUpdate:modelValue":t[5]||(t[5]=l=>s.paymentMethod=l),value:"momo",type:"radio",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[v,s.paymentMethod]]),t[20]||(t[20]=e("label",{for:"momo",class:"ml-3 block text-sm font-medium text-gray-700"}," Mobile Money (MTN MoMo, Airtel Money) ",-1))]),e("div",_,[o(e("input",{id:"card","onUpdate:modelValue":t[6]||(t[6]=l=>s.paymentMethod=l),value:"card",type:"radio",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[v,s.paymentMethod]]),t[21]||(t[21]=e("label",{for:"card",class:"ml-3 block text-sm font-medium text-gray-700"}," Credit/Debit Card ",-1))]),e("div",z,[o(e("input",{id:"bank","onUpdate:modelValue":t[7]||(t[7]=l=>s.paymentMethod=l),value:"bank",type:"radio",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[v,s.paymentMethod]]),t[22]||(t[22]=e("label",{for:"bank",class:"ml-3 block text-sm font-medium text-gray-700"}," Bank Transfer ",-1))])]),s.paymentMethod==="momo"?(a(),r("div",H,[t[23]||(t[23]=e("label",{for:"momoPhone",class:"block text-sm font-medium text-gray-700"}," Mobile Money Phone Number ",-1)),o(e("input",{id:"momoPhone","onUpdate:modelValue":t[8]||(t[8]=l=>s.momoPhone=l),type:"tel",class:"input-field mt-1",placeholder:"Enter your mobile money number"},null,512),[[n,s.momoPhone]])])):y("",!0),s.paymentMethod==="card"?(a(),r("div",J,[e("div",null,[t[24]||(t[24]=e("label",{for:"cardNumber",class:"block text-sm font-medium text-gray-700"}," Card Number ",-1)),o(e("input",{id:"cardNumber","onUpdate:modelValue":t[9]||(t[9]=l=>s.cardNumber=l),type:"text",class:"input-field mt-1",placeholder:"1234 5678 9012 3456"},null,512),[[n,s.cardNumber]])]),e("div",Q,[e("div",null,[t[25]||(t[25]=e("label",{for:"expiryDate",class:"block text-sm font-medium text-gray-700"}," Expiry Date ",-1)),o(e("input",{id:"expiryDate","onUpdate:modelValue":t[10]||(t[10]=l=>s.expiryDate=l),type:"text",class:"input-field mt-1",placeholder:"MM/YY"},null,512),[[n,s.expiryDate]])]),e("div",null,[t[26]||(t[26]=e("label",{for:"cvv",class:"block text-sm font-medium text-gray-700"}," CVV ",-1)),o(e("input",{id:"cvv","onUpdate:modelValue":t[11]||(t[11]=l=>s.cvv=l),type:"text",class:"input-field mt-1",placeholder:"123"},null,512),[[n,s.cvv]])])])])):y("",!0),s.paymentMethod==="bank"?(a(),r("div",X,t[27]||(t[27]=[e("h4",{class:"font-medium text-gray-900 mb-2"},"Bank Transfer Details",-1),e("div",{class:"text-sm text-gray-600 space-y-1"},[e("p",null,[e("strong",null,"Bank:"),d(" Bank of Kigali")]),e("p",null,[e("strong",null,"Account Name:"),d(" Kigali GasGo Ltd")]),e("p",null,[e("strong",null,"Account Number:"),d(" *********")]),e("p",null,[e("strong",null,"Reference:"),d(" Your order ID will be provided")])],-1)]))):y("",!0)]),m.value?(a(),r("div",Z,[e("p",$,i(m.value),1)])):y("",!0),e("div",ee,[e("button",{type:"submit",disabled:p.value,class:"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"},[p.value?(a(),r("span",se,"Processing...")):(a(),r("span",le,"Place Order & Pay (RWF "+i(c.value.toLocaleString())+")",1))],8,te)])],32)])])])]))}});export{ae as default};
