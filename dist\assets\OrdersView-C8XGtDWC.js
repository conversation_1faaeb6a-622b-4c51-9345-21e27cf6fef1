import{d as t,c as s,b as r,o as a}from"./index-CC71qafK.js";const o={class:"min-h-screen bg-gray-50"},n=t({__name:"OrdersView",setup(d){return(l,e)=>(a(),s("div",o,e[0]||(e[0]=[r('<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><h1 class="text-3xl font-bold text-gray-900 mb-8">Seller Orders</h1><div class="bg-white rounded-lg shadow overflow-hidden"><div class="px-6 py-4 border-b border-gray-200"><h2 class="text-lg font-semibold text-gray-900">Incoming Orders</h2></div><div class="p-6"><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3><p class="mt-1 text-sm text-gray-500">Orders will appear here when customers place them.</p></div></div></div></div>',1)])))}});export{n as default};
